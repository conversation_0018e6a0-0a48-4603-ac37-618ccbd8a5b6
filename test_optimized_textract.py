#!/usr/bin/env python3
"""
Test script for optimized Textract processing.
This script tests the improvements made to the Textract extraction process.
"""

import os
import sys
import time
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app_ext_textract_llm import TextractBedrockAutomation, setup_logging

def test_single_file_processing():
    """Test processing a single file with optimized settings."""
    print("Testing single file processing with optimizations...")
    
    # Setup logging
    logger = setup_logging()
    
    # Configuration
    AWS_REGION = 'us-east-1'
    BUCKET_NAME = 'document-extraction-logistically'
    TEMP_INPUT_PREFIX = 'temp/textract_input'
    TEMP_OUTPUT_PREFIX = 'temp/textract_output'
    
    # Initialize processor
    processor = TextractBedrockAutomation(
        region=AWS_REGION,
        bucket_name=BUCKET_NAME,
        temp_input_prefix=TEMP_INPUT_PREFIX,
        temp_output_prefix=TEMP_OUTPUT_PREFIX,
        logger=logger,
        debug_mode=False
    )
    
    # Test file path (adjust as needed)
    test_file = "data/input_data/extraction/sample_invoice.pdf"
    
    if not os.path.exists(test_file):
        print(f"Test file not found: {test_file}")
        print("Please place a test PDF file at the above location or update the path.")
        return False
    
    start_time = time.time()
    
    try:
        result = processor.process_single_file(test_file)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if result['status'] == 'success':
            print(f"✅ SUCCESS: Processed in {processing_time:.2f} seconds")
            print(f"   Output: {result.get('output_file', 'N/A')}")
            print("   Extracted data:")
            extracted_data = result.get('extracted_data', {})
            for key, value in extracted_data.items():
                print(f"     {key}: {value}")
            return True
        else:
            print(f"❌ FAILED: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        end_time = time.time()
        processing_time = end_time - start_time
        print(f"❌ EXCEPTION after {processing_time:.2f} seconds: {e}")
        return False

def test_parallel_processing():
    """Test parallel processing with multiple files."""
    print("\nTesting parallel processing with multiple files...")
    
    # Setup logging
    logger = setup_logging()
    
    # Configuration
    AWS_REGION = 'us-east-1'
    BUCKET_NAME = 'document-extraction-logistically'
    TEMP_INPUT_PREFIX = 'temp/textract_input'
    TEMP_OUTPUT_PREFIX = 'temp/textract_output'
    
    # Initialize processor
    processor = TextractBedrockAutomation(
        region=AWS_REGION,
        bucket_name=BUCKET_NAME,
        temp_input_prefix=TEMP_INPUT_PREFIX,
        temp_output_prefix=TEMP_OUTPUT_PREFIX,
        logger=logger,
        debug_mode=False
    )
    
    # Test folder path
    test_folder = "data/input_data/extraction"
    
    if not os.path.exists(test_folder):
        print(f"Test folder not found: {test_folder}")
        return False
    
    start_time = time.time()
    
    try:
        # Process with 4 parallel workers, limit to 3 files for testing
        results = processor.process_multiple_files(
            test_folder, 
            max_files=3, 
            max_workers=4
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        successful = sum(1 for r in results if r['status'] == 'success')
        failed = len(results) - successful
        
        print(f"\n📊 PARALLEL PROCESSING RESULTS:")
        print(f"   Total time: {processing_time:.2f} seconds")
        print(f"   Files processed: {len(results)}")
        print(f"   Successful: {successful}")
        print(f"   Failed: {failed}")
        print(f"   Average time per file: {processing_time/len(results):.2f} seconds")
        
        return successful > 0
        
    except Exception as e:
        end_time = time.time()
        processing_time = end_time - start_time
        print(f"❌ PARALLEL PROCESSING EXCEPTION after {processing_time:.2f} seconds: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Optimized Textract Processing")
    print("=" * 50)
    
    # Test 1: Single file processing
    single_file_success = test_single_file_processing()
    
    # Test 2: Parallel processing
    parallel_success = test_parallel_processing()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY:")
    print(f"   Single file processing: {'✅ PASS' if single_file_success else '❌ FAIL'}")
    print(f"   Parallel processing: {'✅ PASS' if parallel_success else '❌ FAIL'}")
    
    if single_file_success and parallel_success:
        print("\n🎉 All tests passed! Optimizations are working correctly.")
        return True
    else:
        print("\n⚠️  Some tests failed. Check the logs for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
