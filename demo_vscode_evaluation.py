#!/usr/bin/env python3
"""
Demo script showing how to run evaluations directly in VS Code terminal.
You can copy and paste these function calls into the VS Code terminal.
"""

# Import the evaluation functions
from app.evaluation_extraction import run_csv_evaluation, run_json_evaluation

def demo_csv_evaluation():
    """Demo CSV evaluation that you can run in VS Code terminal."""
    print("🚀 Running CSV Evaluation Demo")
    print("="*50)
    
    # Run CSV evaluation with default settings
    result = run_csv_evaluation(
        csv_file_path="docs/logistically_attachment_data_with_invoice_info.csv",
        extracted_dir="data/output_data/extraction"
    )
    
    if result:
        print(f"\n✅ Evaluation completed successfully!")
        print(f"📊 Report saved to: {result['output_file']}")
        print(f"📁 Total files evaluated: {result['total_files']}")
        
        print("\n📈 Summary Statistics:")
        for file_name, stats in result['summary'].items():
            print(f"  {file_name}:")
            print(f"    - Accuracy: {stats['accuracy']:.1f}%")
            print(f"    - Correct: {stats['correct']}/{stats['comparable']}")
            print(f"    - NULL: {stats['null_count']}")
    else:
        print("❌ Evaluation failed!")

def demo_json_evaluation():
    """Demo JSON evaluation that you can run in VS Code terminal."""
    print("🚀 Running JSON Evaluation Demo")
    print("="*50)
    
    # Run JSON evaluation with default settings
    result = run_json_evaluation(
        extracted_dir="data/output_data/extraction",
        true_dir="data/true_data/extraction"
    )
    
    if result:
        print(f"\n✅ Evaluation completed successfully!")
        print(f"📊 Report saved to: {result['output_file']}")
        print(f"📁 Total files evaluated: {result['total_files']}")
        
        print("\n📈 Summary Statistics:")
        for file_name, stats in result['summary'].items():
            print(f"  {file_name}:")
            print(f"    - Accuracy: {stats['accuracy']:.1f}%")
            print(f"    - Correct: {stats['correct']}/{stats['comparable']}")
            print(f"    - NULL: {stats['null_count']}")
    else:
        print("❌ Evaluation failed!")

if __name__ == "__main__":
    print("📋 VS Code Terminal Evaluation Demo")
    print("You can run these functions directly in VS Code terminal:")
    print()
    print("1. CSV Evaluation:")
    print("   from app.evaluation_extraction import run_csv_evaluation")
    print("   result = run_csv_evaluation('docs/logistically_attachment_data_with_invoice_info.csv')")
    print()
    print("2. JSON Evaluation:")
    print("   from app.evaluation_extraction import run_json_evaluation")
    print("   result = run_json_evaluation()")
    print()
    print("3. Custom paths:")
    print("   result = run_csv_evaluation(")
    print("       csv_file_path='path/to/your/file.csv',")
    print("       extracted_dir='custom/extraction/dir',")
    print("       output_file='custom/output/report.xlsx'")
    print("   )")
    print()
    
    # Run demos
    demo_csv_evaluation()
    print("\n" + "="*60 + "\n")
    demo_json_evaluation()
