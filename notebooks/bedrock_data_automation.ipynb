{"cells": [{"cell_type": "code", "execution_count": 3, "id": "9d1bc4e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempting to automatically open the SSO authorization page in your default browser.\n", "If the browser does not open or you wish to use a different device to authorize this request, open the following URL:\n", "\n", "https://oidc.us-east-1.amazonaws.com/authorize?response_type=code&client_id=ue5vMoYFlPDKWE5sPC3hznVzLWVhc3QtMQ&redirect_uri=http%3A%2F%2F127.0.0.1%3A46507%2Foauth%2Fcallback&state=e6ac3022-4f39-4b13-bffa-63cf13cf50db&code_challenge_method=S256&scopes=sso%3Aaccount%3Aaccess&code_challenge=dH4cc1yeh35_WGLCcRN7VaG63x7qql0nYV2t9ysFDvY\n", "Successfully logged into Start URL: https://armakuni-us.awsapps.com/start\n"]}], "source": ["!aws sso login --profile DeveloperLearningAccountAccess-************"]}, {"cell_type": "code", "execution_count": 4, "id": "74492f00", "metadata": {}, "outputs": [], "source": ["import boto3, json\n", "from pprint import pprint\n", "\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "client = session.client(\"bedrock-runtime\", region_name=\"us-east-1\")"]}, {"cell_type": "markdown", "id": "1d9e22c6", "metadata": {}, "source": ["# Bedrock Data Automation"]}, {"cell_type": "code", "execution_count": 1, "id": "ea125fd5", "metadata": {}, "outputs": [{"ename": "TokenRetrievalError", "evalue": "Error when retrieving token from sso: <PERSON>ken has expired and refresh failed", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTokenRetrievalError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 140\u001b[0m\n\u001b[1;32m    137\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mProcessing failed.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    139\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m--> 140\u001b[0m     \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[1], line 99\u001b[0m, in \u001b[0;36mmain\u001b[0;34m()\u001b[0m\n\u001b[1;32m     96\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m\n\u001b[1;32m     98\u001b[0m \u001b[38;5;66;03m# Initialize BDA client\u001b[39;00m\n\u001b[0;32m---> 99\u001b[0m bda \u001b[38;5;241m=\u001b[39m \u001b[43mBedrockDataAutomation\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    100\u001b[0m \u001b[43m    \u001b[49m\u001b[43mregion\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mAWS_REGION\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    101\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbucket_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mBUCKET_NAME\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    102\u001b[0m \u001b[43m    \u001b[49m\u001b[43mproject_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mPROJECT_ID\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    103\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtemp_input_prefix\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mTEMP_INPUT_PREFIX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    104\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtemp_output_prefix\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mTEMP_OUTPUT_PREFIX\u001b[49m\n\u001b[1;32m    105\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    107\u001b[0m \u001b[38;5;66;03m# Upload local file to S3\u001b[39;00m\n\u001b[1;32m    108\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUploading \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mLOCAL_INPUT_FILE\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m to S3\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[0;32mIn[1], line 19\u001b[0m, in \u001b[0;36mBedrockDataAutomation.__init__\u001b[0;34m(self, region, bucket_name, project_id, temp_input_prefix, temp_output_prefix)\u001b[0m\n\u001b[1;32m     17\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtemp_output_prefix \u001b[38;5;241m=\u001b[39m temp_output_prefix\n\u001b[1;32m     18\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mproject_id \u001b[38;5;241m=\u001b[39m project_id\n\u001b[0;32m---> 19\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maws_account_id \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_aws_account_id\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[1], line 23\u001b[0m, in \u001b[0;36mBedrockDataAutomation.get_aws_account_id\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m     21\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mget_aws_account_id\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mstr\u001b[39m:\n\u001b[1;32m     22\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Retrieve AWS account ID.\"\"\"\u001b[39;00m\n\u001b[0;32m---> 23\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msts_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_caller_identity\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mAccount\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/client.py:601\u001b[0m, in \u001b[0;36mClientCreator._create_api_method.<locals>._api_call\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    597\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\n\u001b[1;32m    598\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpy_operation_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m() only accepts keyword arguments.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    599\u001b[0m     )\n\u001b[1;32m    600\u001b[0m \u001b[38;5;66;03m# The \"self\" in this scope is referring to the BaseClient.\u001b[39;00m\n\u001b[0;32m--> 601\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_api_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43moperation_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/context.py:123\u001b[0m, in \u001b[0;36mwith_current_context.<locals>.decorator.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    121\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m hook:\n\u001b[1;32m    122\u001b[0m     hook()\n\u001b[0;32m--> 123\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/client.py:1056\u001b[0m, in \u001b[0;36mBaseClient._make_api_call\u001b[0;34m(self, operation_name, api_params)\u001b[0m\n\u001b[1;32m   1052\u001b[0m     maybe_compress_request(\n\u001b[1;32m   1053\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmeta\u001b[38;5;241m.\u001b[39mconfig, request_dict, operation_model\n\u001b[1;32m   1054\u001b[0m     )\n\u001b[1;32m   1055\u001b[0m     apply_request_checksum(request_dict)\n\u001b[0;32m-> 1056\u001b[0m     http, parsed_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1057\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation_model\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrequest_dict\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrequest_context\u001b[49m\n\u001b[1;32m   1058\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1060\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmeta\u001b[38;5;241m.\u001b[39mevents\u001b[38;5;241m.\u001b[39memit(\n\u001b[1;32m   1061\u001b[0m     \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mafter-call.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mservice_id\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00moperation_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m,\n\u001b[1;32m   1062\u001b[0m     http_response\u001b[38;5;241m=\u001b[39mhttp,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1065\u001b[0m     context\u001b[38;5;241m=\u001b[39mrequest_context,\n\u001b[1;32m   1066\u001b[0m )\n\u001b[1;32m   1068\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m http\u001b[38;5;241m.\u001b[39mstatus_code \u001b[38;5;241m>\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m300\u001b[39m:\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/client.py:1080\u001b[0m, in \u001b[0;36mBaseClient._make_request\u001b[0;34m(self, operation_model, request_dict, request_context)\u001b[0m\n\u001b[1;32m   1078\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_make_request\u001b[39m(\u001b[38;5;28mself\u001b[39m, operation_model, request_dict, request_context):\n\u001b[1;32m   1079\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1080\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_endpoint\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmake_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43moperation_model\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrequest_dict\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1081\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m   1082\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmeta\u001b[38;5;241m.\u001b[39mevents\u001b[38;5;241m.\u001b[39memit(\n\u001b[1;32m   1083\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mafter-call-error.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_service_model\u001b[38;5;241m.\u001b[39mservice_id\u001b[38;5;241m.\u001b[39mhyphenize()\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00moperation_model\u001b[38;5;241m.\u001b[39mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m,\n\u001b[1;32m   1084\u001b[0m             exception\u001b[38;5;241m=\u001b[39me,\n\u001b[1;32m   1085\u001b[0m             context\u001b[38;5;241m=\u001b[39mrequest_context,\n\u001b[1;32m   1086\u001b[0m         )\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/endpoint.py:118\u001b[0m, in \u001b[0;36mEndpoint.make_request\u001b[0;34m(self, operation_model, request_dict)\u001b[0m\n\u001b[1;32m    112\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mmake_request\u001b[39m(\u001b[38;5;28mself\u001b[39m, operation_model, request_dict):\n\u001b[1;32m    113\u001b[0m     logger\u001b[38;5;241m.\u001b[39mdebug(\n\u001b[1;32m    114\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMaking request for \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m with params: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    115\u001b[0m         operation_model,\n\u001b[1;32m    116\u001b[0m         request_dict,\n\u001b[1;32m    117\u001b[0m     )\n\u001b[0;32m--> 118\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_send_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest_dict\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moperation_model\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/endpoint.py:195\u001b[0m, in \u001b[0;36mEndpoint._send_request\u001b[0;34m(self, request_dict, operation_model)\u001b[0m\n\u001b[1;32m    193\u001b[0m context \u001b[38;5;241m=\u001b[39m request_dict[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcontext\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[1;32m    194\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_update_retries_context(context, attempts)\n\u001b[0;32m--> 195\u001b[0m request \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest_dict\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moperation_model\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    196\u001b[0m success_response, exception \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_response(\n\u001b[1;32m    197\u001b[0m     request, operation_model, context\n\u001b[1;32m    198\u001b[0m )\n\u001b[1;32m    199\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_needs_retry(\n\u001b[1;32m    200\u001b[0m     attempts,\n\u001b[1;32m    201\u001b[0m     operation_model,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    204\u001b[0m     exception,\n\u001b[1;32m    205\u001b[0m ):\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/endpoint.py:131\u001b[0m, in \u001b[0;36mEndpoint.create_request\u001b[0;34m(self, params, operation_model)\u001b[0m\n\u001b[1;32m    129\u001b[0m     service_id \u001b[38;5;241m=\u001b[39m operation_model\u001b[38;5;241m.\u001b[39mservice_model\u001b[38;5;241m.\u001b[39mservice_id\u001b[38;5;241m.\u001b[39mhyphenize()\n\u001b[1;32m    130\u001b[0m     event_name \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mrequest-created.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mservice_id\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00moperation_model\u001b[38;5;241m.\u001b[39mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m--> 131\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_event_emitter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43memit\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    132\u001b[0m \u001b[43m        \u001b[49m\u001b[43mevent_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    133\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    134\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation_model\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    135\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    136\u001b[0m prepared_request \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprepare_request(request)\n\u001b[1;32m    137\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m prepared_request\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/hooks.py:412\u001b[0m, in \u001b[0;36mEventAliaser.emit\u001b[0;34m(self, event_name, **kwargs)\u001b[0m\n\u001b[1;32m    410\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21memit\u001b[39m(\u001b[38;5;28mself\u001b[39m, event_name, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    411\u001b[0m     aliased_event_name \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_alias_event_name(event_name)\n\u001b[0;32m--> 412\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_emitter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43memit\u001b[49m\u001b[43m(\u001b[49m\u001b[43maliased_event_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/hooks.py:256\u001b[0m, in \u001b[0;36mHierarchicalEmitter.emit\u001b[0;34m(self, event_name, **kwargs)\u001b[0m\n\u001b[1;32m    245\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21memit\u001b[39m(\u001b[38;5;28mself\u001b[39m, event_name, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    246\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    247\u001b[0m \u001b[38;5;124;03m    Emit an event by name with arguments passed as keyword args.\u001b[39;00m\n\u001b[1;32m    248\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    254\u001b[0m \u001b[38;5;124;03m             handlers.\u001b[39;00m\n\u001b[1;32m    255\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 256\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_emit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mevent_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/hooks.py:239\u001b[0m, in \u001b[0;36mHierarchicalEmitter._emit\u001b[0;34m(self, event_name, kwargs, stop_on_response)\u001b[0m\n\u001b[1;32m    237\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m handler \u001b[38;5;129;01min\u001b[39;00m handlers_to_call:\n\u001b[1;32m    238\u001b[0m     logger\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mEvent \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m: calling handler \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m, event_name, handler)\n\u001b[0;32m--> 239\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mhandler\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    240\u001b[0m     responses\u001b[38;5;241m.\u001b[39mappend((handler, response))\n\u001b[1;32m    241\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m stop_on_response \u001b[38;5;129;01mand\u001b[39;00m response \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/signers.py:108\u001b[0m, in \u001b[0;36mRequestSigner.handler\u001b[0;34m(self, operation_name, request, **kwargs)\u001b[0m\n\u001b[1;32m    103\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mhandler\u001b[39m(\u001b[38;5;28mself\u001b[39m, operation_name\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, request\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    104\u001b[0m     \u001b[38;5;66;03m# This is typically hooked up to the \"request-created\" event\u001b[39;00m\n\u001b[1;32m    105\u001b[0m     \u001b[38;5;66;03m# from a client's event emitter.  When a new request is created\u001b[39;00m\n\u001b[1;32m    106\u001b[0m     \u001b[38;5;66;03m# this method is invoked to sign the request.\u001b[39;00m\n\u001b[1;32m    107\u001b[0m     \u001b[38;5;66;03m# Don't call this method directly.\u001b[39;00m\n\u001b[0;32m--> 108\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msign\u001b[49m\u001b[43m(\u001b[49m\u001b[43moperation_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/signers.py:191\u001b[0m, in \u001b[0;36mRequestSigner.sign\u001b[0;34m(self, operation_name, request, region_name, signing_type, expires_in, signing_name)\u001b[0m\n\u001b[1;32m    185\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_resolve_identity_cache(\n\u001b[1;32m    186\u001b[0m         kwargs,\n\u001b[1;32m    187\u001b[0m         signing_context[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124midentity_cache\u001b[39m\u001b[38;5;124m'\u001b[39m],\n\u001b[1;32m    188\u001b[0m         signing_context[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcache_key\u001b[39m\u001b[38;5;124m'\u001b[39m],\n\u001b[1;32m    189\u001b[0m     )\n\u001b[1;32m    190\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 191\u001b[0m     auth \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_auth_instance\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    192\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m UnknownSignatureVersionError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    193\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m signing_type \u001b[38;5;241m!=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstandard\u001b[39m\u001b[38;5;124m'\u001b[39m:\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/signers.py:312\u001b[0m, in \u001b[0;36mRequestSigner.get_auth_instance\u001b[0;34m(self, signing_name, region_name, signature_version, request_credentials, **kwargs)\u001b[0m\n\u001b[1;32m    310\u001b[0m frozen_credentials \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    311\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m credentials \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m--> 312\u001b[0m     frozen_credentials \u001b[38;5;241m=\u001b[39m \u001b[43mcredentials\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_frozen_credentials\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    313\u001b[0m kwargs[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcredentials\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m frozen_credentials\n\u001b[1;32m    314\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39mREQUIRES_REGION:\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/credentials.py:667\u001b[0m, in \u001b[0;36mRefreshableCredentials.get_frozen_credentials\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    633\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mget_frozen_credentials\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[1;32m    634\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Return immutable credentials.\u001b[39;00m\n\u001b[1;32m    635\u001b[0m \n\u001b[1;32m    636\u001b[0m \u001b[38;5;124;03m    The ``access_key``, ``secret_key``, and ``token`` properties\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    665\u001b[0m \n\u001b[1;32m    666\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 667\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_refresh\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    668\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_frozen_credentials\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/credentials.py:554\u001b[0m, in \u001b[0;36mRefreshableCredentials._refresh\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    550\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m\n\u001b[1;32m    551\u001b[0m     is_mandatory_refresh \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrefresh_needed(\n\u001b[1;32m    552\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_mandatory_refresh_timeout\n\u001b[1;32m    553\u001b[0m     )\n\u001b[0;32m--> 554\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_protected_refresh\u001b[49m\u001b[43m(\u001b[49m\u001b[43mis_mandatory\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mis_mandatory_refresh\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    555\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m\n\u001b[1;32m    556\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/credentials.py:570\u001b[0m, in \u001b[0;36mRefreshableCredentials._protected_refresh\u001b[0;34m(self, is_mandatory)\u001b[0m\n\u001b[1;32m    566\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_protected_refresh\u001b[39m(\u001b[38;5;28mself\u001b[39m, is_mandatory):\n\u001b[1;32m    567\u001b[0m     \u001b[38;5;66;03m# precondition: this method should only be called if you've acquired\u001b[39;00m\n\u001b[1;32m    568\u001b[0m     \u001b[38;5;66;03m# the self._refresh_lock.\u001b[39;00m\n\u001b[1;32m    569\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 570\u001b[0m         metadata \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_refresh_using\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    571\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[1;32m    572\u001b[0m         period_name \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmandatory\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m is_mandatory \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124madvisory\u001b[39m\u001b[38;5;124m'\u001b[39m\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/credentials.py:719\u001b[0m, in \u001b[0;36mCachedCredentialFetcher.fetch_credentials\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    718\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mfetch_credentials\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m--> 719\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_cached_credentials\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/credentials.py:729\u001b[0m, in \u001b[0;36mCachedCredentialFetcher._get_cached_credentials\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    727\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_load_from_cache()\n\u001b[1;32m    728\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m response \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m--> 729\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_credentials\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    730\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_write_to_cache(response)\n\u001b[1;32m    731\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/credentials.py:2246\u001b[0m, in \u001b[0;36mSSOCredentialFetcher._get_credentials\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   2244\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_token_provider:\n\u001b[1;32m   2245\u001b[0m     initial_token_data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_token_provider\u001b[38;5;241m.\u001b[39mload_token()\n\u001b[0;32m-> 2246\u001b[0m     token \u001b[38;5;241m=\u001b[39m \u001b[43minitial_token_data\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_frozen_token\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mtoken\n\u001b[1;32m   2247\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   2248\u001b[0m     token \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_token_loader(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_start_url)[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124maccessToken\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/tokens.py:89\u001b[0m, in \u001b[0;36mDeferredRefreshableToken.get_frozen_token\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m     88\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mget_frozen_token\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m---> 89\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_refresh\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     90\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_frozen_token\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/tokens.py:102\u001b[0m, in \u001b[0;36mDeferredRefreshableToken._refresh\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_refresh_lock\u001b[38;5;241m.\u001b[39macquire(block_for_refresh):\n\u001b[1;32m    101\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 102\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_protected_refresh\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    103\u001b[0m     \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m    104\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_refresh_lock\u001b[38;5;241m.\u001b[39mrelease()\n", "File \u001b[0;32m~/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/botocore/tokens.py:129\u001b[0m, in \u001b[0;36mDeferredRefreshableToken._protected_refresh\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    125\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m\n\u001b[1;32m    127\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_is_expired():\n\u001b[1;32m    128\u001b[0m     \u001b[38;5;66;03m# Fresh credentials should never be expired\u001b[39;00m\n\u001b[0;32m--> 129\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m TokenRetrievalError(\n\u001b[1;32m    130\u001b[0m         provider\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmethod,\n\u001b[1;32m    131\u001b[0m         error_msg\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mToken has expired and refresh failed\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    132\u001b[0m     )\n", "\u001b[0;31mTokenRetrievalError\u001b[0m: Error when retrieving token from sso: Token has expired and refresh failed"]}], "source": ["import json\n", "import time\n", "import boto3\n", "import os\n", "from botocore.exceptions import ClientError\n", "\n", "os.chdir(r\"/home/<USER>/Documents/repositories/logistically\")\n", "\n", "class BedrockDataAutomation:\n", "    def __init__(self, region: str, bucket_name: str, project_id: str, temp_input_prefix: str, temp_output_prefix: str):\n", "        session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "        self.bda_client = session.client('bedrock-data-automation-runtime', region_name=region)\n", "        self.s3_client = session.client('s3', region_name=region)\n", "        self.sts_client = session.client('sts')\n", "        self.bucket_name = bucket_name\n", "        self.temp_input_prefix = temp_input_prefix\n", "        self.temp_output_prefix = temp_output_prefix\n", "        self.project_id = project_id\n", "        self.aws_account_id = self.get_aws_account_id()\n", "\n", "    def get_aws_account_id(self) -> str:\n", "        \"\"\"Retrieve AWS account ID.\"\"\"\n", "        return self.sts_client.get_caller_identity().get('Account')\n", "\n", "    def upload_file_to_s3(self, local_file_path: str) -> str:\n", "        \"\"\"Upload local file to S3 and return S3 URI.\"\"\"\n", "        try:\n", "            file_name = os.path.basename(local_file_path)\n", "            s3_key = f\"{self.temp_input_prefix}/{file_name}\"\n", "            self.s3_client.upload_file(local_file_path, self.bucket_name, s3_key)\n", "            return f\"s3://{self.bucket_name}/{s3_key}\"\n", "        except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "            print(f\"Error uploading file to S3: {e}\")\n", "            raise\n", "\n", "    def download_file_from_s3(self, s3_uri: str, local_output_path: str):\n", "        \"\"\"Download file from S3 to local path.\"\"\"\n", "        try:\n", "            s3_key = '/'.join(s3_uri.split('/')[3:])\n", "            self.s3_client.download_file(self.bucket_name, s3_key, local_output_path)\n", "            with open(local_output_path, 'r') as f:\n", "                return json.load(f)\n", "        except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "            print(f\"Error downloading file from S3: {e}\")\n", "            raise\n", "\n", "    def invoke_data_automation(self, input_s3_uri: str, output_s3_uri: str) -> dict:\n", "        \"\"\"Invoke Bedrock Data Automation async processing.\"\"\"\n", "        try:\n", "            data_automation_arn = f\"arn:aws:bedrock:{self.bda_client.meta.region_name}:{self.aws_account_id}:data-automation-project/{self.project_id}\"\n", "            # Updated profile ARN - replace with the correct one from your AWS account\n", "            profile_arn = f\"arn:aws:bedrock:us-east-1:************:data-automation-profile/us.data-automation-v1\"\n", "            response = self.bda_client.invoke_data_automation_async(\n", "                inputConfiguration={'s3Uri': input_s3_uri},\n", "                outputConfiguration={'s3Uri': output_s3_uri},\n", "                dataAutomationConfiguration={\n", "                    'dataAutomationProjectArn': data_automation_arn,\n", "                    'stage': 'LIVE'  # Updated to 'LIVE' - verify and adjust based on your project\n", "                },\n", "                dataAutomationProfileArn=profile_arn\n", "            )\n", "            return response\n", "        except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "            print(f\"Error invoking BDA: {e}\")\n", "            raise\n", "\n", "    def check_job_status(self, invocation_arn: str) -> dict:\n", "        \"\"\"Check the status of the BDA processing job.\"\"\"\n", "        try:\n", "            while True:\n", "                status_response = self.bda_client.get_data_automation_status(invocationArn=invocation_arn)\n", "                status = status_response.get('status')\n", "                print(f\"Job status: {status}\")\n", "                \n", "                if status in ['Success', 'Failed']:\n", "                    return status_response\n", "                time.sleep(10)\n", "        except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "            print(f\"Error checking job status: {e}\")\n", "            raise\n", "\n", "def main():\n", "    # Configuration\n", "    AWS_REGION = 'us-east-1'  # Ensure region supports BDA (us-east-1 or us-west-2)\n", "    BUCKET_NAME = 'bda-logistically-demo-bucket'  # Replace with your S3 bucket\n", "    LOCAL_INPUT_FILE = r'data/input_data/extraction/11173426_carrier_invoice.pdf'  # Path to local PDF file\n", "    FILE_NAME = os.path.basename(LOCAL_INPUT_FILE).split('.', 1)[0]\n", "    LOCAL_OUTPUT_FILE = f'data/output_data/extraction/{FILE_NAME}.json'  # Path to save output JSON\n", "    PROJECT_ID = '76559db4147e'  # Verify this ID exists in your account\n", "    TEMP_INPUT_PREFIX = 'temp/input'  # Temporary S3 input path\n", "    TEMP_OUTPUT_PREFIX = 'temp/output'  # Temporary S3 output path\n", "\n", "    # Validate local input file\n", "    if not os.path.exists(LOCAL_INPUT_FILE):\n", "        print(f\"Input file {LOCAL_INPUT_FILE} not found.\")\n", "        return\n", "\n", "    # Initialize BDA client\n", "    bda = BedrockDataAutomation(\n", "        region=AWS_REGION,\n", "        bucket_name=BUCKET_NAME,\n", "        project_id=PROJECT_ID,\n", "        temp_input_prefix=TEMP_INPUT_PREFIX,\n", "        temp_output_prefix=TEMP_OUTPUT_PREFIX\n", "    )\n", "\n", "    # Upload local file to S3\n", "    print(f\"Uploading {LOCAL_INPUT_FILE} to S3\")\n", "    input_s3_uri = bda.upload_file_to_s3(LOCAL_INPUT_FILE)\n", "    output_s3_uri = f\"s3://{BUCKET_NAME}/{TEMP_OUTPUT_PREFIX}/\"\n", "\n", "    # Invoke BDA processing\n", "    print(f\"Starting BDA processing for {input_s3_uri}\")\n", "    response = bda.invoke_data_automation(input_s3_uri, output_s3_uri)\n", "    invocation_arn = response.get('invocationArn')\n", "\n", "    # Check job status,\n", "    aws_access_key_id=\"********************\",\n", "    aws_secret_access_key=\"FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9\"\n", "    status_response = bda.check_job_status(invocation_arn)\n", "    \n", "    if status_response.get('status') == 'Success':\n", "        # Get output S3 URI from the correct location in response\n", "        output_s3_uri = status_response.get('outputS3Uri') or status_response.get('outputConfiguration', {}).get('s3Uri')\n", "        print(f\"Processing complete. Output S3 URI: {output_s3_uri}\")\n", "        \n", "        if output_s3_uri:\n", "            print(f\"Downloading results to {LOCAL_OUTPUT_FILE}\")\n", "            # Download and display results\n", "            results = bda.download_file_from_s3(output_s3_uri, LOCAL_OUTPUT_FILE)\n", "            print(\"Extracted Data:\")\n", "            print(json.dumps(results, indent=4))\n", "        else:\n", "            print(\"No output S3 URI found in status response.\")\n", "            print(\"Status response:\", json.dumps(status_response, indent=2))\n", "    else:\n", "        print(\"Processing failed.\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 6, "id": "e308bd16", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error downloading file: An error occurred (ForbiddenException) when calling the GetRoleCredentials operation: No access\n"]}, {"data": {"text/plain": ["False"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Code to download s3 file\n", "import boto3\n", "from urllib.parse import urlparse\n", "import os\n", "\n", "def download_from_s3_uri(s3_uri, local_file_path, aws_profile=None):\n", "    \"\"\"\n", "    Download a file from S3 using S3 URI format (s3://bucket/key)\n", "    \n", "    Args:\n", "        s3_uri (str): S3 URI in format s3://bucket-name/path/to/file\n", "        local_file_path (str): Local path where file should be saved\n", "        aws_profile (str, optional): AWS profile name to use\n", "    \n", "    Returns:\n", "        bool: True if successful, False otherwise\n", "    \"\"\"\n", "    try:\n", "        # Parse S3 URI\n", "        parsed_uri = urlparse(s3_uri)\n", "        bucket_name = parsed_uri.netloc\n", "        s3_key = parsed_uri.path.lstrip('/')\n", "        \n", "        # Create S3 client\n", "        if aws_profile:\n", "            session = boto3.Session(profile_name=aws_profile)\n", "            s3_client = session.client('s3')\n", "        else:\n", "            s3_client = boto3.client('s3')\n", "        \n", "        # Create directory if it doesn't exist\n", "        os.makedirs(os.path.dirname(local_file_path), exist_ok=True)\n", "        \n", "        # Download the file\n", "        s3_client.download_file(bucket_name, s3_key, local_file_path)\n", "        print(f\"Successfully downloaded {s3_uri} to {local_file_path}\")\n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"Error downloading file: {e}\")\n", "        return False\n", "\n", "# Example usage\n", "s3_uri = \"s3://bda-logistically-demo-bucket/temp/output//2cdbdcff-01e1-4a55-8d69-ccd1ab38fa87/0/custom_output/0/result.json\"\n", "local_path = \"/home/<USER>/Documents/repositories/logistically/data/output_data/extraction/11173426_carrier_invoice_output.json\"\n", "\n", "download_from_s3_uri(s3_uri, local_path, aws_profile=\"DeveloperLearningAccountAccess-************\")"]}, {"cell_type": "code", "execution_count": null, "id": "9a2ccda7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "id": "1d8f153d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File /home/<USER>/Documents/repositories/logistically/data/input_data/extraction/processed/11173426_carrier_invoice.pdf uploaded successfully to document-extraction-logistically/sample/sampleFile2.pdf\n"]}], "source": ["import boto3\n", "from botocore.exceptions import ClientError\n", "import os\n", "\n", "def upload_file_to_s3(file_path, bucket_name, object_name=None):\n", "    \"\"\"\n", "    Upload a file to an S3 bucket\n", "    \n", "    :param file_path: Path to the file to upload\n", "    :param bucket_name: Name of the S3 bucket\n", "    :param object_name: S3 object name. If not specified, file name is used\n", "    :return: True if file was uploaded successfully, else False\n", "    \"\"\"\n", "    # If S3 object_name was not specified, use file name\n", "    if object_name is None:\n", "        object_name = os.path.basename(file_path)\n", "\n", "    # Create S3 client\n", "    s3_client = boto3.client('s3',\n", "        region_name=\"us-east-1\",\n", "        aws_access_key_id=\"********************\",\n", "        aws_secret_access_key=\"FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9\")\n", "    \n", "    try:\n", "        # Upload the file\n", "        s3_client.upload_file(file_path, bucket_name, object_name)\n", "        print(f\"File {file_path} uploaded successfully to {bucket_name}/{object_name}\")\n", "        return True\n", "    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "        print(f\"Error uploading file: {e}\")\n", "        return False\n", "    except FileNotFoundError:\n", "        print(f\"File {file_path} not found\")\n", "        return False\n", "\n", "# Example usage\n", "if __name__ == \"__main__\":\n", "    # Replace these values with your own\n", "    file_path = \"/home/<USER>/Documents/repositories/logistically/data/input_data/extraction/processed/11173426_carrier_invoice.pdf\"\n", "    bucket_name = \"document-extraction-logistically\"\n", "    s3_object_name = \"sample/sampleFile2.pdf\"  # Optional: specify S3 path\n", "    \n", "    upload_file_to_s3(file_path, bucket_name, s3_object_name)"]}, {"cell_type": "code", "execution_count": 7, "id": "04c5c689", "metadata": {}, "outputs": [], "source": ["import boto3\n", "\n", "# Initialize S3 client\n", "s3 = boto3.client('s3',\n", "        region_name=\"us-east-1\",\n", "        aws_access_key_id=\"********************\",\n", "        aws_secret_access_key=\"FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9\")\n", "\n", "# Specify bucket, file, and local path\n", "bucket_name = 'document-extraction-logistically'\n", "file_key = \"sample/sampleFile2.pdf\"\n", "local_path = 'downloadedFile.pdf'\n", "\n", "# Download file\n", "s3.download_file(bucket_name, file_key, local_path)"]}, {"cell_type": "code", "execution_count": 5, "id": "f9954c7b", "metadata": {}, "outputs": [{"data": {"text/plain": ["'/home/<USER>/Documents/repositories/logistically'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["pwd"]}, {"cell_type": "code", "execution_count": null, "id": "8a591a89", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}