{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d185de67", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# loading from .env\n", "\n", "import os\n", "from dotenv import load_dotenv\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": null, "id": "74492f00", "metadata": {}, "outputs": [], "source": ["import boto3, json\n", "from pprint import pprint\n", "\n", "AWS_ACCESS_KEY_ID=os.environ.get(\"AWS_ACCESS_KEY_ID\")\n", "AWS_SECRET_ACCESS_KEY=os.environ.get(\"AWS_SECRET_ACCESS_KEY\")\n", "\n", "# session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "client = boto3.client(\"bedrock-runtime\", region_name=\"us-east-1\",\n", "    aws_access_key_id=AWS_ACCESS_KEY_ID,\n", "    aws_secret_access_key=AWS_SECRET_ACCESS_KEY)"]}, {"cell_type": "markdown", "id": "1d9e22c6", "metadata": {}, "source": ["# Bedrock Data Automation"]}, {"cell_type": "code", "execution_count": 31, "id": "ea125fd5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Uploading /home/<USER>/Documents/repositories/logistically/data/input_data/extraction/processed/11173426_carrier_invoice.pdf to S3\n", "Starting BDA processing for s3://document-extraction-logistically/temp/input/11173426_carrier_invoice.pdf\n", "Job status: InProgress\n", "Job status: InProgress\n", "Job status: InProgress\n", "Job status: Success\n", "Processing complete. Output S3 URI: s3://document-extraction-logistically/temp/output//ba086b6a-2bdd-4f8f-ba15-4d2fc7d95723/job_metadata.json\n", "Downloading results to data/output_data/extraction/11173426_carrier_invoice.json\n", "Extracted Data:\n", "{\n", "    \"job_id\": \"ba086b6a-2bdd-4f8f-ba15-4d2fc7d95723\",\n", "    \"job_status\": \"PROCESSED\",\n", "    \"semantic_modality\": \"DOCUMENT\",\n", "    \"output_metadata\": [\n", "        {\n", "            \"asset_id\": 0,\n", "            \"asset_input_path\": {\n", "                \"s3_bucket\": \"document-extraction-logistically\",\n", "                \"s3_key\": \"temp/input/11173426_carrier_invoice.pdf\"\n", "            },\n", "            \"segment_metadata\": [\n", "                {\n", "                    \"custom_output_path\": \"s3://document-extraction-logistically/temp/output//ba086b6a-2bdd-4f8f-ba15-4d2fc7d95723/0/custom_output/0/result.json\",\n", "                    \"custom_output_status\": \"MATCH\"\n", "                }\n", "            ]\n", "        }\n", "    ]\n", "}\n"]}], "source": ["import json\n", "import time\n", "import boto3\n", "import os\n", "from botocore.exceptions import ClientError\n", "\n", "os.chdir(r\"/home/<USER>/Documents/repositories/logistically\")\n", "\n", "class BedrockDataAutomation:\n", "    def __init__(self, region: str, bucket_name: str, project_id: str, temp_input_prefix: str, temp_output_prefix: str):\n", "        # session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "        self.bda_client = boto3.client('bedrock-data-automation-runtime', region_name=\"us-east-1\",\n", "    aws_access_key_id=\"********************\",\n", "    aws_secret_access_key=\"FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9\")\n", "        self.s3_client = boto3.client('s3', region_name=region,\n", "            aws_access_key_id=\"********************\",\n", "            aws_secret_access_key=\"FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9\")\n", "        self.sts_client = boto3.client('sts',\n", "            aws_access_key_id=\"********************\",\n", "            aws_secret_access_key=\"FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9\")\n", "        self.bucket_name = bucket_name\n", "        self.temp_input_prefix = temp_input_prefix\n", "        self.temp_output_prefix = temp_output_prefix\n", "        self.project_id = project_id\n", "        self.aws_account_id = self.get_aws_account_id()\n", "\n", "    def get_aws_account_id(self) -> str:\n", "        \"\"\"Retrieve AWS account ID.\"\"\"\n", "        return self.sts_client.get_caller_identity().get('Account')\n", "\n", "    def upload_file_to_s3(self, local_file_path: str) -> str:\n", "        \"\"\"Upload local file to S3 and return S3 URI.\"\"\"\n", "        try:\n", "            file_name = os.path.basename(local_file_path)\n", "            s3_key = f\"{self.temp_input_prefix}/{file_name}\"\n", "            self.s3_client.upload_file(local_file_path, self.bucket_name, s3_key)\n", "            return f\"s3://{self.bucket_name}/{s3_key}\"\n", "        except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "            print(f\"Error uploading file to S3: {e}\")\n", "            raise\n", "\n", "    def download_file_from_s3(self, s3_uri: str, local_output_path: str):\n", "        \"\"\"Download file from S3 to local path.\"\"\"\n", "        try:\n", "            s3_key = '/'.join(s3_uri.split('/')[3:])\n", "            self.s3_client.download_file(self.bucket_name, s3_key, local_output_path)\n", "            with open(local_output_path, 'r') as f:\n", "                return json.load(f)\n", "        except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "            print(f\"Error downloading file from S3: {e}\")\n", "            raise\n", "\n", "    def invoke_data_automation(self, input_s3_uri: str, output_s3_uri: str) -> dict:\n", "        \"\"\"Invoke Bedrock Data Automation async processing.\"\"\"\n", "        try:\n", "            \"\"\n", "            data_automation_arn = f\"arn:aws:bedrock:us-east-1:************:data-automation-project/367d069118f3\"\n", "            # Updated profile ARN - replace with the correct one from your AWS account\n", "            profile_arn = f\"arn:aws:bedrock:us-east-1:************:data-automation-profile/us.data-automation-v1\"\n", "            response = self.bda_client.invoke_data_automation_async(\n", "                inputConfiguration={'s3Uri': input_s3_uri},\n", "                outputConfiguration={'s3Uri': output_s3_uri},\n", "                # dataAutomationConfiguration={\n", "                #     'dataAutomationProjectArn': data_automation_arn,\n", "                #     'stage': 'LIVE'\n", "                # },\n", "                dataAutomationProfileArn=profile_arn,\n", "                blueprints=[\n", "                    {\n", "                        'blueprintArn': 'arn:aws:bedrock:us-east-1:************:blueprint/bb4379fda68a',\n", "                        # 'version': '1',  # Optional: Specify the blueprint version\n", "                        # 'stage': 'LIVE'  # Optional: Specify the blueprint stage (LIVE or DEVELOPMENT)\n", "                    }\n", "                ]\n", "            )\n", "            return response\n", "        except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "            print(f\"Error invoking BDA: {e}\")\n", "            raise\n", "\n", "    def check_job_status(self, invocation_arn: str) -> dict:\n", "        \"\"\"Check the status of the BDA processing job.\"\"\"\n", "        try:\n", "            while True:\n", "                status_response = self.bda_client.get_data_automation_status(invocationArn=invocation_arn)\n", "                status = status_response.get('status')\n", "                print(f\"Job status: {status}\")\n", "                 \n", "                if status in ['Success', 'Failed']:\n", "                    return status_response\n", "                time.sleep(10)\n", "        except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "            print(f\"Error checking job status: {e}\")\n", "            raise\n", "\n", "def main():\n", "    # Configuration\n", "    AWS_REGION = 'us-east-1'  # Ensure region supports BDA (us-east-1 or us-west-2)\n", "    BUCKET_NAME = 'document-extraction-logistically'  # Replace with your S3 bucket\n", "    LOCAL_INPUT_FILE = r'/home/<USER>/Documents/repositories/logistically/data/input_data/extraction/processed/11173426_carrier_invoice.pdf'  # Path to local PDF file\n", "    FILE_NAME = os.path.basename(LOCAL_INPUT_FILE).split('.', 1)[0]\n", "    LOCAL_OUTPUT_FILE = f'data/output_data/extraction/{FILE_NAME}.json'  # Path to save output JSON\n", "    PROJECT_ID = 'ad4a57a0c572'  # Verify this ID exists in your account\n", "    TEMP_INPUT_PREFIX = 'temp/input'  # Temporary S3 input path\n", "    TEMP_OUTPUT_PREFIX = 'temp/output'  # Temporary S3 output path\n", "\n", "    # Validate local input file\n", "    if not os.path.exists(LOCAL_INPUT_FILE):\n", "        print(f\"Input file {LOCAL_INPUT_FILE} not found.\")\n", "        return\n", "\n", "    # Initialize BDA client\n", "    bda = BedrockDataAutomation(\n", "        region=AWS_REGION,\n", "        bucket_name=BUCKET_NAME,\n", "        project_id=PROJECT_ID,\n", "        temp_input_prefix=TEMP_INPUT_PREFIX,\n", "        temp_output_prefix=TEMP_OUTPUT_PREFIX\n", "    )\n", " \n", "    # Upload local file to S3\n", "    print(f\"Uploading {LOCAL_INPUT_FILE} to S3\")\n", "    input_s3_uri = bda.upload_file_to_s3(LOCAL_INPUT_FILE)\n", "    output_s3_uri = f\"s3://{BUCKET_NAME}/{TEMP_OUTPUT_PREFIX}/\"\n", "\n", "    # Invoke BDA processing\n", "    print(f\"Starting BDA processing for {input_s3_uri}\")\n", "    response = bda.invoke_data_automation(input_s3_uri, output_s3_uri)\n", "    invocation_arn = response.get('invocationArn')\n", "\n", "    # Check job status\n", "    status_response = bda.check_job_status(invocation_arn)\n", "    \n", "    if status_response.get('status') == 'Success':\n", "        # Get output S3 URI from the correct location in response\n", "        output_s3_uri = status_response.get('outputS3Uri') or status_response.get('outputConfiguration', {}).get('s3Uri')\n", "        print(f\"Processing complete. Output S3 URI: {output_s3_uri}\")\n", "        \n", "        if output_s3_uri:\n", "            print(f\"Downloading results to {LOCAL_OUTPUT_FILE}\")\n", "            # Download and display results\n", "            results = bda.download_file_from_s3(output_s3_uri, LOCAL_OUTPUT_FILE)\n", "            print(\"Extracted Data:\")\n", "            print(json.dumps(results, indent=4))\n", "        else:\n", "            print(\"No output S3 URI found in status response.\")\n", "            print(\"Status response:\", json.dumps(status_response, indent=2))\n", "    else:\n", "        print(\"Processing failed.\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": null, "id": "bdfadee7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7596e0e9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c2aefe6e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8a99a445", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f8c44207", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 32, "id": "e308bd16", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully downloaded s3://document-extraction-logistically/temp/output//ba086b6a-2bdd-4f8f-ba15-4d2fc7d95723/0/custom_output/0/result.json to /home/<USER>/Documents/repositories/logistically/data/output_data/extraction/12345.json.json\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["# Code to download s3 file\n", "import boto3\n", "from urllib.parse import urlparse\n", "import os\n", "\n", "def download_from_s3_uri(s3_uri, local_file_path, aws_profile=None):\n", "    \"\"\"\n", "    Download a file from S3 using S3 URI format (s3://bucket/key)\n", "    \n", "    Args:\n", "        s3_uri (str): S3 URI in format s3://bucket-name/path/to/file\n", "        local_file_path (str): Local path where file should be saved\n", "        aws_profile (str, optional): AWS profile name to use\n", "    s3://document-extraction-logistically/temp/output//9eafb990-c8f7-4260-9f71-d3b3190531b3/0/standard_output/0/result.json\n", "    Returns:\n", "        bool: True if successful, False otherwise\n", "    \"\"\"\n", "    try:\n", "        # Parse S3 URI\n", "        parsed_uri = urlparse(s3_uri)\n", "        bucket_name = parsed_uri.netloc\n", "        s3_key = parsed_uri.path.lstrip('/')\n", "        \n", "        # Create S3 client\n", "        if aws_profile:\n", "            session = boto3.Session(profile_name=aws_profile)\n", "            s3_client = session.client('s3', region_name=\"us-east-1\",\n", "            aws_access_key_id=\"********************\",\n", "            aws_secret_access_key=\"FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9\")\n", "        else:\n", "            s3_client = boto3.client('s3', region_name=\"us-east-1\",\n", "                aws_access_key_id=\"********************\",\n", "                aws_secret_access_key=\"FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9\")\n", "        \n", "        # Create directory if it doesn't exist\n", "        os.makedirs(os.path.dirname(local_file_path), exist_ok=True)\n", "        \n", "        # Download the file\n", "        s3_client.download_file(bucket_name, s3_key, local_file_path)\n", "        print(f\"Successfully downloaded {s3_uri} to {local_file_path}\")\n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"Error downloading file: {e}\")\n", "        return False\n", "\n", "# Example usage\n", "s3_uri = \"s3://document-extraction-logistically/temp/output//ba086b6a-2bdd-4f8f-ba15-4d2fc7d95723/0/custom_output/0/result.json\"\n", "local_path = \"/home/<USER>/Documents/repositories/logistically/data/output_data/extraction/12345.json.json\"\n", "\n", "download_from_s3_uri(s3_uri, local_path)"]}, {"cell_type": "code", "execution_count": null, "id": "9a2ccda7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}