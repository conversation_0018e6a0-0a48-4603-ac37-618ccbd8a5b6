{"cells": [{"cell_type": "code", "execution_count": null, "id": "d117ea39", "metadata": {}, "outputs": [], "source": ["!aws sso login --profile DeveloperLearningAccountAccess-************"]}, {"cell_type": "code", "execution_count": 2, "id": "be288041", "metadata": {}, "outputs": [], "source": ["import boto3, json\n", "from pprint import pprint\n", "\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "client = session.client(\"bedrock-runtime\", region_name=\"us-east-1\")"]}, {"cell_type": "code", "execution_count": 1, "id": "4fc71c23", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'text': \"You are very helpful assistant which extracts data in key-value pair from given document. To achieve that follow below steps\\n\\nStep 1:\\nRead the data given to you in user prompt in following structure:\\n=== NON-TABLE TEXT ===\\n<non-table-text>\\n\\n=== TABLE 1 ===\\n<table-1-data>\\n=== TABLE 2 ===\\n<table-2-data>\\n...\\n=== TABLE n ===\\n<table-n-data>\\n\\nStep 2:\\nRefer to image for location of given data and it's context\\n\\nStep 3:\\nExtract complete data in key-value pair from given data. \\n\\nStep 4:\\nOnce again make sure that each and every item is covered in extracted key-value pairs, If not then include it in key-value pair.\\n\\nStep 5:\\nUse tool call to with extracted data\"}]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# system prompt\n", "\n", "with open('system_prompt.txt', 'r') as f:\n", "    system_prompt = f.read()\n", "\n", "system_prompt = [{\"text\": system_prompt}]\n", "system_prompt"]}, {"cell_type": "code", "execution_count": 3, "id": "3e6a399d", "metadata": {}, "outputs": [], "source": ["# user prompt\n", "# text\n", "\n", "with open('structured_text.json', 'r') as f:\n", "    structured_text = json.load(f)\n", "\n", "# image\n", "\n", "image_path = \"document.jpeg\"\n", "\n", "with open(image_path, \"rb\") as f:\n", "    image_bytes = f.read()\n", "    \n", "image_format = image_path.split(\".\")[-1].lower()\n", "\n", "messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [\n", "            {\"text\": \"Analyze the provided image and describe its contents.\"},\n", "            {\"text\": structured_text},\n", "            {\"image\": {\"format\": image_format, \"source\": {\"bytes\": image_bytes}}}\n", "        ]\n", "    }\n", "]"]}, {"cell_type": "code", "execution_count": 4, "id": "eb6fba81", "metadata": {}, "outputs": [], "source": ["# response format\n", "\n", "toolConfig={\n", "    \"tools\": [\n", "        {\n", "            'toolSpec': {\n", "                'name': 'All_details',\n", "                'description': 'This tool received everything present in given image as key value pairs',\n", "                'inputSchema': {\n", "                    'json': {\n", "                        'title': 'Details',\n", "                        'type': 'object',\n", "                        'properties': {\n", "                            'content': {\n", "                                'title': 'Content',\n", "                                'type': 'string',\n", "                                'description': 'content inside the image in key-value pair',\n", "                                \"additionalProperties\": {\n", "                                        \"type\": \"string\"\n", "                                    }\n", "                            }\n", "                        },\n", "                        'required': [\n", "                            'content'\n", "                        ]\n", "                    }\n", "                },\n", "            }\n", "        }\n", "    ],\n", "    \"toolChoice\": {\n", "        \"tool\":{\"name\":\"All_details\"}\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 1, "id": "da7cc66a", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'client' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# main api call\u001b[39;00m\n\u001b[0;32m----> 3\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43mclient\u001b[49m\u001b[38;5;241m.\u001b[39mconverse(\n\u001b[1;32m      4\u001b[0m     modelId\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124marn:aws:bedrock:us-east-1:************:inference-profile/us.anthropic.claude-sonnet-4-20250514-v1:0\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m      5\u001b[0m     system\u001b[38;5;241m=\u001b[39msystem_prompt,\n\u001b[1;32m      6\u001b[0m     messages\u001b[38;5;241m=\u001b[39mmessages,\n\u001b[1;32m      7\u001b[0m     inferenceConfig\u001b[38;5;241m=\u001b[39m{\n\u001b[1;32m      8\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmaxTokens\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m4096\u001b[39m,\n\u001b[1;32m      9\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtemperature\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m0\u001b[39m,\n\u001b[1;32m     10\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtopP\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m1\u001b[39m,\n\u001b[1;32m     11\u001b[0m     },\n\u001b[1;32m     12\u001b[0m     toolConfig\u001b[38;5;241m=\u001b[39mtoolConfig,\n\u001b[1;32m     13\u001b[0m )\n\u001b[1;32m     15\u001b[0m output \u001b[38;5;241m=\u001b[39m response[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124moutput\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmessage\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;241m0\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtoolUse\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124minput\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[1;32m     16\u001b[0m pprint(json\u001b[38;5;241m.\u001b[39mloads(output[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m]))\n", "\u001b[0;31mNameError\u001b[0m: name 'client' is not defined"]}], "source": ["# main api call\n", "\n", "response = client.converse(\n", "    modelId=\"arn:aws:bedrock:us-east-1:************:inference-profile/us.anthropic.claude-sonnet-4-20250514-v1:0\",\n", "    system=system_prompt,\n", "    messages=messages,\n", "    inferenceConfig={\n", "        'maxTokens': 4096,\n", "        'temperature': 0,\n", "        'topP': 1,\n", "    },\n", "    toolConfig=toolConfig,\n", ")\n", "\n", "output = response['output']['message']['content'][0]['toolUse']['input']\n", "pprint(json.loads(output[\"content\"]))"]}, {"cell_type": "code", "execution_count": null, "id": "ca55df4f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}