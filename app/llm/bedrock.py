from typing import Any, Dict, Optional

from app.llm.base import ModelConfigBase
from app.core.configuration import settings


class BedrockConverseModel(ModelConfigBase):
    def __init__(
        self,   
        buffer_token: int,
        answer_token: int,
        memory_token: Optional[int],
        max_token_limit: int,
        model_id: str,
        model_kwargs: Optional[Dict[str, Any]] = None,
        streaming: Optional[bool] = True,
    ):
        model = self._initialize_model(model_id, model_kwargs, streaming or True)

        super().__init__(
            buffer_token=buffer_token,
            answer_token=answer_token,
            max_token_limit=max_token_limit,
            model=model,
            model_id=model_id,
            memory_token=memory_token,
        )

    def _initialize_model(
        self,
        model_id: str,
        model_kwargs: Optional[Dict[str, Any]] = None,
        streaming: bool = True,
    ) -> ChatBedrock:
        """
        Initialize a BedrockChat language model.

        Args:
            model_id (str): The unique identifier for the BedrockChat model.
            model_kwargs (Optional[Dict[str, Any]]): Additional model keyword arguments (default is None).
            streaming (Optional[bool]): Indicates whether to use streaming for responses (default is True).

        Returns:  nvbn  hgv
            BedrockChat: An initialized BedrockChat instance for the specified model.
        """

        try:
            return ChatBedrock(
                model_id=model_id,
                model_kwargs=model_kwargs,
                streaming=streaming,
                region_name=settings.AWS_REGION
            )
        except Exception as exc:
            logger.warning(f"error in initializing {model_id}\n {str(exc)}")

    @classmethod
    def create_from_model_config(
        cls,
        buffer_token: int,
        answer_token: int,
        max_token_limit: int,
        model_id: str,
        model_kwargs: Optional[Dict[str, Any]] = None,
        memory_token: Optional[int] = 0,
        streaming: Optional[
            bool
        ] = True,  # turn it off for model which do not support streaming
    ) -> "BedrockChatModelConfig":
        """
        Create a configuration for a BedrockChat model with the provided parameters.

        Args:
            buffer_token (int): The token used for buffering.
            answer_token (int): The token representing an answer.
            max_token_limit (int): The maximum token limit for the model.
            model_id (str): The ID of the BedrockChat model.
            model_kwargs (Optional[Dict[str, Any]]): Additional model-specific keyword arguments.
            streaming (Optional[bool]): Whether streaming is enabled (default is True).
            memory_token (Optional[int]): The token used for memory.

        Returns:
            BedrockChatModelConfig: A configuration object for a BedrockChat model with the specified parameters.
        """
        return cls(
            buffer_token=buffer_token,
            answer_token=answer_token,
            max_token_limit=max_token_limit,
            model_id=model_id,
            model_kwargs=model_kwargs,
            streaming=streaming,
            memory_token=memory_token,
        )

 
claude_v3 = BedrockChatModelConfig.create_from_model_config(
    buffer_token=150,
    answer_token=500,
    memory_token=500,
    max_token_limit=100000,
    model_id="anthropic.claude-3-sonnet-20240229-v1:0",
    model_kwargs={
        "temperature": 0.5,
        "max_tokens": 500,
    },
)
   
response = client.converse(
    modelId="arn:aws:bedrock:us-east-1:222634373323:inference-profile/us.anthropic.claude-sonnet-4-20250514-v1:0",
    system=system_prompt,
    messages=messages,
    inferenceConfig={
        'maxTokens': 4096,
        'temperature': 0,
        'topP': 1,
    },
    # toolConfig=toolConfig, # tools to be added later 
)