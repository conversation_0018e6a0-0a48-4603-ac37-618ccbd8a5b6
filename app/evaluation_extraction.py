import json
import os
import glob
import argparse
import pandas as pd
import re
import csv
from datetime import datetime
from pathlib import Path

try:
    from openpyxl import Workbook
    from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    print("Warning: openpyxl not available. Will create CSV report instead.")

def setup_logging():
    """Setup basic logging for evaluation."""
    import logging
    
    # Create logs directory if it doesn't exist
    logs_dir = Path("data/logs")
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    # Create log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = logs_dir / f"evaluation_{timestamp}.log"
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # Also log to console
        ]
    )
    
    return logging.getLogger(__name__)

def load_json_file(file_path):
    """Load and parse JSON file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def extract_inference_fields(data):
    """Extract inference result fields from the JSON data."""
    if not data:
        return {}

    # If 'inference_result' exists, use original logic
    if 'inference_result' in data:
        inference_result = data['inference_result']
        extracted_fields = {}
        for key, value in inference_result.items():
            if key == 'SERVICES_TABLE':
                if isinstance(value, list) and len(value) > 0:
                    for i, service in enumerate(value):
                        for service_key, service_value in service.items():
                            field_name = f"SERVICES_TABLE[{i}].{service_key}"
                            extracted_fields[field_name] = service_value
            elif key == 'TAX':
                if isinstance(value, list):
                    for i, tax_value in enumerate(value):
                        field_name = f"TAX[{i}]"
                        extracted_fields[field_name] = tax_value
            else:
                extracted_fields[key] = value
        return extracted_fields
    else:
        # If no 'inference_result', treat top-level keys as fields (flat structure)
        return dict(data)

def extract_confidence_values(data):
    """Extract confidence values from explainability_info."""
    confidence_values = {}

    if not data or 'explainability_info' not in data:
        return confidence_values

    explainability_info = data['explainability_info']
    if not isinstance(explainability_info, list) or len(explainability_info) == 0:
        return confidence_values

    # Get the first explainability info object
    explain_data = explainability_info[0]

    for key, value in explain_data.items():
        if isinstance(value, dict) and 'confidence' in value:
            # Convert confidence to integer percentage
            confidence_values[key] = f"{int(value['confidence'] * 100)}%"
        elif isinstance(value, list):
            # Handle arrays like TAX
            for i, item in enumerate(value):
                if isinstance(item, dict) and 'confidence' in item:
                    field_name = f"{key}[{i}]"
                    confidence_values[field_name] = f"{int(item['confidence'] * 100)}%"

        # Handle SERVICES_TABLE confidence values
        if key == 'SERVICES_TABLE' and isinstance(value, list):
            for i, service in enumerate(value):
                if isinstance(service, dict):
                    for service_key, service_value in service.items():
                        if isinstance(service_value, dict) and 'confidence' in service_value:
                            field_name = f"SERVICES_TABLE[{i}].{service_key}"
                            confidence_values[field_name] = f"{int(service_value['confidence'] * 100)}%"

    return confidence_values

def load_csv_file(csv_file_path):
    """Load CSV file with true data."""
    try:
        df = pd.read_csv(csv_file_path)
        print(f"Loaded CSV file with {len(df)} rows")
        return df
    except Exception as e:
        print(f"Error loading CSV file {csv_file_path}: {e}")
        return None

def load_tracking_csv(tracking_csv_path="data/output_data/extraction/file_tracking.csv"):
    """Load the file tracking CSV that maps extracted files to PDF locations."""
    try:
        if not os.path.exists(tracking_csv_path):
            print(f"Warning: Tracking CSV file not found at {tracking_csv_path}")
            return {}

        tracking_data = {}
        with open(tracking_csv_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                # Use extracted JSON filename as key
                json_filename = row.get('extracted_json_filename', '')
                if json_filename:
                    tracking_data[json_filename] = {
                        'original_pdf_path': row.get('original_pdf_path', ''),
                        'moved_pdf_path': row.get('moved_pdf_path', ''),
                        'extracted_json_path': row.get('extracted_json_path', ''),
                        'pdf_filename': row.get('pdf_filename', ''),
                        'invoice_number': row.get('invoice_number', ''),
                        'vendor_name': row.get('vendor_name', ''),
                        'processing_status': row.get('processing_status', ''),
                        'timestamp': row.get('timestamp', '')
                    }

        print(f"Loaded tracking data for {len(tracking_data)} files")
        return tracking_data
    except Exception as e:
        print(f"Error loading tracking CSV file {tracking_csv_path}: {e}")
        return {}

def extract_original_filename_from_extracted(extracted_filename):
    """
    Extract the original filename from the extracted filename.
    Handles formats like:
    - original_name_extracted.json -> original_name
    - original_name_invoice123_extracted.json -> original_name
    - original_name_invoice123_extracted_v2.json -> original_name
    - original_name_ABC-123_extracted.json -> original_name (special chars converted to underscores)
    """
    # Remove .json extension
    base_name = extracted_filename.replace('.json', '')

    # Remove version suffix if present (e.g., _v2, _v3)
    base_name = re.sub(r'_v\d+$', '', base_name)

    # Remove _extracted suffix
    base_name = base_name.replace('_extracted', '')

    # If there's still an underscore followed by what looks like an invoice number, remove it
    # This handles the format: original_name_invoice123 or original_name_ABC-123 (converted to ABC_123)
    parts = base_name.split('_')
    if len(parts) > 1:
        # Check if the last part looks like an invoice number
        # Invoice numbers typically contain digits and may have special chars converted to underscores
        last_part = parts[-1]

        # More sophisticated check for invoice number patterns
        is_likely_invoice = (
            # Contains digits
            any(c.isdigit() for c in last_part) and
            # Is alphanumeric with underscores/dashes (from special char conversion)
            re.match(r'^[A-Za-z0-9_-]+$', last_part) and
            # Not just a single letter or very short (likely part of original name)
            len(last_part) > 1 and
            # Not all letters (likely part of original name)
            not last_part.isalpha() and
            # Special case: if it starts with NOINV, it's a fallback invoice number
            not last_part.startswith('NOINV')
        )

        # Special handling for fallback invoice numbers (NOINV_xxxxxx)
        if last_part.startswith('NOINV_'):
            is_likely_invoice = True

        if is_likely_invoice:
            # Remove the last part (likely invoice number)
            base_name = '_'.join(parts[:-1])

    return base_name

def find_csv_match_by_invoice_number(df, invoice_number, original_file_name, logger=None):
    """Find matching row in CSV by invoice number only. If invoice number not found, return None."""
    if df is None:
        if logger:
            logger.warning(f"CSV dataframe is None for file: {original_file_name}")
        return None

    # Only match by invoice number - if not found, return None for NULL handling
    if invoice_number and str(invoice_number).strip():
        clean_invoice_number = str(invoice_number).strip()
        if logger:
            logger.info(f"  Looking for invoice number '{clean_invoice_number}' in CSV data")

        # Try exact match first
        invoice_matches = df[df['invoice_number'].astype(str).str.strip() == clean_invoice_number]

        if not invoice_matches.empty:
            if logger:
                logger.info(f"  ✅ Found exact match for invoice number '{clean_invoice_number}'")
            return invoice_matches.iloc[0]
        else:
            if logger:
                logger.warning(f"  ❌ No CSV match found for invoice number '{clean_invoice_number}'")
                # Show available invoice numbers for debugging
                available_invoices = df['invoice_number'].astype(str).str.strip().unique()[:10]  # Show first 10
                logger.info(f"  Available invoice numbers (first 10): {list(available_invoices)}")
    else:
        if logger:
            logger.warning(f"  No invoice number provided for file: {original_file_name}")

    # If no invoice number match found, return None (will trigger NULL handling)
    return None

def normalize_value_for_comparison(value):
    """
    Normalize a value for smart comparison by:
    1. Converting to string and stripping whitespace
    2. Handling null/empty equivalents
    3. Extracting only alphanumeric characters for comparison
    4. Converting to lowercase for case-insensitive comparison
    5. Handling special country equivalents (US/USA)
    """
    if value is None:
        return None

    # Convert to string and strip
    value_str = str(value).strip()

    # Handle various null/empty representations
    null_equivalents = {
        '', 'none', 'null', 'nan', 'n/a', 'na', 'nil', 'empty',
        'undefined', 'void', '-', '--', '---', 'not available',
        'not applicable', 'missing', 'unknown'
    }

    if value_str.lower() in null_equivalents:
        return None

    # Handle special country equivalents before alphanumeric extraction
    country_equivalents = {
        'us': 'usa',
        'usa': 'usa',
        'united states': 'usa',
        'united states of america': 'usa'
    }

    # Check if this looks like a country value
    value_lower = value_str.lower()
    if value_lower in country_equivalents:
        return country_equivalents[value_lower]

    # Extract only alphanumeric characters and convert to lowercase
    # This handles cases like "PO Box 721" vs "P.O. Box 721"
    alphanumeric_only = ''.join(char.lower() for char in value_str if char.isalnum())

    # If after removing non-alphanumeric chars we get empty string, treat as None
    if not alphanumeric_only:
        return None

    return alphanumeric_only

def compare_values(extracted_value, true_value):
    """
    Smart comparison of two values that:
    1. Normalizes both values by extracting only alphanumeric characters
    2. Treats various null/empty representations as equivalent
    3. Performs case-insensitive comparison
    4. Handles cases like "PO Box 721" vs "P.O. Box 721"
    """
    # Normalize both values
    normalized_extracted = normalize_value_for_comparison(extracted_value)
    normalized_true = normalize_value_for_comparison(true_value)

    # Handle None values (including normalized nulls)
    if normalized_extracted is None and normalized_true is None:
        return True
    if normalized_extracted is None or normalized_true is None:
        return False

    # Compare normalized alphanumeric strings
    return normalized_extracted == normalized_true

def evaluate_single_file_with_csv(extracted_file, csv_df, file_name, logger):
    """Evaluate a single extracted file against CSV true data."""
    logger.info(f"Evaluating: {extracted_file} against CSV data")

    # Load extracted file
    extracted_data = load_json_file(extracted_file)
    if not extracted_data:
        logger.error(f"Failed to load extracted data file: {extracted_file}")
        return None

    # Extract fields from JSON
    extracted_invoice_number = extracted_data.get('invoice_number', '')
    extracted_invoice_date = extracted_data.get('invoice_date', '')
    extracted_invoice_amount = extracted_data.get('invoice_amount', '')
    extracted_vendor_name = extracted_data.get('vendor_name', '')

    # Extract remit_to fields
    remit_to = extracted_data.get('remit_to', {})
    if remit_to is None:
        remit_to = {}

    # Handle case where remit_to might be a string instead of a dictionary
    if not isinstance(remit_to, dict):
        # Check if it's a string representation of null/empty
        if isinstance(remit_to, str) and remit_to.lower() in ['null', 'none', '', 'undefined']:
            logger.debug(f"remit_to field is string '{remit_to}', treating as empty dict")
        else:
            logger.warning(f"remit_to field is not a dictionary (type: {type(remit_to)}, value: {remit_to}), treating as empty dict")
        remit_to = {}

    extracted_remit_to_name = remit_to.get('remit_to_name', '')
    extracted_remit_to_address1 = remit_to.get('remit_to_address1', '')
    extracted_remit_to_address_2 = remit_to.get('remit_to_address_2', '')
    extracted_remit_to_city = remit_to.get('remit_to_city', '')
    extracted_remit_to_state_province = remit_to.get('remit_to_state_province', '')
    extracted_remit_to_postal_code = remit_to.get('remit_to_postal_code', '')
    extracted_remit_to_country = remit_to.get('remit_to_country', '')

    # Find matching row in CSV
    csv_row = find_csv_match_by_invoice_number(csv_df, extracted_invoice_number, file_name, logger)

    # Field mapping for comparison
    field_comparisons = [
        ('invoice_number', extracted_invoice_number),
        ('invoice_date', extracted_invoice_date),
        ('invoice_amount', extracted_invoice_amount),
        ('vendor_name', extracted_vendor_name),
        ('remit_to_name', extracted_remit_to_name),
        ('remit_to_address1', extracted_remit_to_address1),
        ('remit_to_address_2', extracted_remit_to_address_2),
        ('remit_to_city', extracted_remit_to_city),
        ('remit_to_state_province', extracted_remit_to_state_province),
        ('remit_to_postal_code', extracted_remit_to_postal_code),
        ('remit_to_country', extracted_remit_to_country)
    ]

    results = []

    if csv_row is None:
        logger.warning(f"No matching CSV row found for file: {file_name}, invoice: {extracted_invoice_number}")
        # Create NULL results for all fields when no CSV match is found
        for field_name, extracted_value in field_comparisons:
            results.append({
                'File Name': file_name,
                'Field Name': field_name,
                'True Data Value': "NULL",
                'Extracted Value': extracted_value,
                'Confidence': "",
                'Correct': "NULL"  # Special value to indicate no comparison possible
            })
    else:
        # Normal comparison when CSV row is found
        for field_name, extracted_value in field_comparisons:
            true_value = csv_row.get(field_name, '')
            is_correct = compare_values(extracted_value, true_value)

            results.append({
                'File Name': file_name,
                'Field Name': field_name,
                'True Data Value': true_value,
                'Extracted Value': extracted_value,
                'Confidence': "",  # No confidence data available for CSV comparison
                'Correct': "TRUE" if is_correct else "FALSE"
            })

    return results

def evaluate_single_file(extracted_file, true_file, file_name, logger):
    """Evaluate a single file pair and return comparison results."""
    logger.info(f"Evaluating: {extracted_file} vs {true_file}")

    # Load both files
    extracted_data = load_json_file(extracted_file)
    true_data = load_json_file(true_file)

    if not extracted_data or not true_data:
        logger.error(f"Failed to load data files")
        return None

    # Extract inference fields and confidence values
    extracted_fields = extract_inference_fields(extracted_data)
    true_fields = extract_inference_fields(true_data)
    confidence_values = extract_confidence_values(extracted_data)

    # Map output fields to true data fields
    field_mapping = {
        'invoice_amount': 'TOTAL',
        'invoice_date': 'DATE',
        'invoice_number': 'ID'
    }

    results = []
    for extracted_field, true_field in field_mapping.items():
        extracted_value = extracted_fields.get(extracted_field, "")
        true_value = true_fields.get(true_field, "")
        is_correct = compare_values(extracted_value, true_value)
        confidence = confidence_values.get(extracted_field, "")

        results.append({
            'File Name': file_name,
            'Field Name': f"{extracted_field} <-> {true_field}",
            'True Data Value': true_value,
            'Extracted Value': extracted_value,
            'Confidence': confidence,
            'Correct': "TRUE" if is_correct else "FALSE"
        })

    return results

def create_csv_report(all_results, output_file, logger):
    """Create CSV report as fallback when openpyxl is not available."""
    logger.info(f"Creating CSV report: {output_file}")

    import csv

    # Load tracking data for additional columns
    tracking_data = load_tracking_csv()

    # Change extension to .csv
    csv_file = output_file.replace('.xlsx', '.csv')

    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)

        # Write header for combined report with additional columns
        writer.writerow(['File Name', 'Field Name', 'True Data Value', 'Extracted Value', 'Confidence', 'Correct',
                        'Moved PDF Path', 'Extracted JSON Path', 'Invoice Number', 'Vendor Name', 'Processing Status'])

        # Combine all results
        for file_name, results in all_results.items():
            # Get tracking info for this file (try to find matching extracted JSON)
            tracking_info = None
            for json_filename, info in tracking_data.items():
                # Try to match by extracting original filename from JSON filename
                original_from_json = extract_original_filename_from_extracted(json_filename)
                if original_from_json == file_name:
                    tracking_info = info
                    break

            for result in results:
                # Add tracking data to EVERY row
                if tracking_info:
                    moved_pdf_path = tracking_info.get('moved_pdf_path', '')
                    extracted_json_path = tracking_info.get('extracted_json_path', '')
                    invoice_number = tracking_info.get('invoice_number', '')
                    vendor_name = tracking_info.get('vendor_name', '')
                    processing_status = tracking_info.get('processing_status', '')
                else:
                    moved_pdf_path = extracted_json_path = invoice_number = vendor_name = processing_status = ''

                writer.writerow([
                    result['File Name'],
                    result['Field Name'],
                    str(result['True Data Value']),
                    str(result['Extracted Value']),
                    str(result['Confidence']),
                    result['Correct'],
                    moved_pdf_path,
                    extracted_json_path,
                    invoice_number,
                    vendor_name,
                    processing_status
                ])

        # Write statistics summary - Column-wise
        writer.writerow([])
        writer.writerow(['=== STATISTICS SUMMARY (BY COLUMN) ==='])

        # Collect all field names
        all_field_names = set()
        for results in all_results.values():
            for result in results:
                all_field_names.add(result['Field Name'])

        # Sort field names for consistent ordering
        sorted_field_names = sorted(all_field_names)

        # Calculate statistics for each field/column
        for field_name in sorted_field_names:
            # Collect all results for this field across all files
            field_results = []
            for results in all_results.values():
                field_results.extend([r for r in results if r['Field Name'] == field_name])

            if field_results:  # Only process if we have results for this field
                correct_count = sum(1 for r in field_results if r['Correct'] == "TRUE")
                null_count = sum(1 for r in field_results if r['Correct'] == "NULL")
                total_count = len(field_results)
                wrong_count = total_count - correct_count - null_count
                comparable_count = total_count - null_count
                accuracy = round((correct_count / comparable_count * 100) if comparable_count > 0 else 0, 2)

                writer.writerow([f"{field_name} - Total Values", total_count])
                writer.writerow([f"{field_name} - Correct", correct_count])
                writer.writerow([f"{field_name} - Wrong", wrong_count])
                writer.writerow([f"{field_name} - NULL (No CSV Match)", null_count])
                writer.writerow([f"{field_name} - Accuracy (%)", accuracy])
                writer.writerow([])

    logger.info(f"CSV report saved to: {csv_file}")

def create_excel_report(all_results, output_file, logger):
    """Create Excel report with formatting and PDF hyperlinks."""
    logger.info(f"Creating Excel report: {output_file}")

    # Load tracking data for PDF hyperlinks
    tracking_data = load_tracking_csv()

    # Create workbook and worksheet
    wb = Workbook()
    ws = wb.active
    ws.title = "Evaluation Report"

    # Add headers with additional tracking columns
    headers = ['File Name', 'Field Name', 'True Data Value', 'Extracted Value', 'Confidence', 'Correct',
               'PDF Link', 'Moved PDF Path', 'Extracted JSON Path', 'Invoice Number', 'Vendor Name', 'Processing Status']
    ws.append(headers)

    # Style headers
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_font = Font(color="FFFFFF", bold=True)

    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=1, column=col)
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = Alignment(horizontal="center", vertical="center")

    # Add data rows from all files
    row_idx = 2

    for file_name, results in all_results.items():
        # Get tracking info for this file (try to find matching extracted JSON)
        tracking_info = None
        for json_filename, info in tracking_data.items():
            # Try to match by extracting original filename from JSON filename
            original_from_json = extract_original_filename_from_extracted(json_filename)
            if original_from_json == file_name:
                tracking_info = info
                break

        for result in results:
            ws.cell(row=row_idx, column=1, value=result['File Name'])
            ws.cell(row=row_idx, column=2, value=result['Field Name'])
            ws.cell(row=row_idx, column=3, value=str(result['True Data Value']))
            ws.cell(row=row_idx, column=4, value=str(result['Extracted Value']))
            ws.cell(row=row_idx, column=5, value=str(result['Confidence']))
            ws.cell(row=row_idx, column=6, value=result['Correct'])

            # Add PDF hyperlink and tracking info for EVERY row
            if tracking_info:
                pdf_path = tracking_info.get('moved_pdf_path') or tracking_info.get('original_pdf_path', '')
                if pdf_path and os.path.exists(pdf_path):
                    # Create hyperlink to PDF file
                    pdf_cell = ws.cell(row=row_idx, column=7)
                    pdf_cell.hyperlink = f"file:///{os.path.abspath(pdf_path)}"
                    pdf_cell.value = "Open PDF"
                    pdf_cell.font = Font(color="0000FF", underline="single")  # Blue underlined text
                else:
                    ws.cell(row=row_idx, column=7, value="PDF not found")

                # Add new tracking columns to EVERY row
                ws.cell(row=row_idx, column=8, value=tracking_info.get('moved_pdf_path', ''))  # Moved PDF Path
                ws.cell(row=row_idx, column=9, value=tracking_info.get('extracted_json_path', ''))  # Extracted JSON Path
                ws.cell(row=row_idx, column=10, value=tracking_info.get('invoice_number', ''))  # Invoice Number
                ws.cell(row=row_idx, column=11, value=tracking_info.get('vendor_name', ''))  # Vendor Name
                ws.cell(row=row_idx, column=12, value=tracking_info.get('processing_status', ''))  # Processing Status
            else:
                ws.cell(row=row_idx, column=7, value="No tracking data")
                ws.cell(row=row_idx, column=8, value="")  # Moved PDF Path
                ws.cell(row=row_idx, column=9, value="")  # Extracted JSON Path
                ws.cell(row=row_idx, column=10, value="")  # Invoice Number
                ws.cell(row=row_idx, column=11, value="")  # Vendor Name
                ws.cell(row=row_idx, column=12, value="")  # Processing Status

            # Apply formatting based on correctness
            if result['Correct'] == "TRUE":
                fill_color = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")  # Light green
            elif result['Correct'] == "NULL":
                fill_color = PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")  # Light blue
            else:
                fill_color = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")  # Light red

            # Create thick black border for invoice grouping
            thick_border = Border(
                left=Side(border_style="thick", color="000000"),
                right=Side(border_style="thick", color="000000"),
                top=Side(border_style="thick", color="000000"),
                bottom=Side(border_style="thick", color="000000")
            )

            # Apply fill and border to the entire row
            for col in range(1, len(headers) + 1):
                cell = ws.cell(row=row_idx, column=col)
                cell.fill = fill_color
                cell.border = thick_border

            row_idx += 1

    # Add statistics at the end - Column-wise
    stats_start_row = row_idx + 2

    # Add statistics header
    ws.cell(row=stats_start_row, column=1, value="STATISTICS SUMMARY (BY COLUMN)")
    ws.cell(row=stats_start_row, column=1).font = Font(bold=True, size=14)
    stats_start_row += 1

    # Collect all field names
    all_field_names = set()
    for results in all_results.values():
        for result in results:
            all_field_names.add(result['Field Name'])

    # Sort field names for consistent ordering
    sorted_field_names = sorted(all_field_names)

    # Add statistics for each field/column
    for field_name in sorted_field_names:
        # Collect all results for this field across all files
        field_results = []
        for results in all_results.values():
            field_results.extend([r for r in results if r['Field Name'] == field_name])

        if field_results:  # Only process if we have results for this field
            correct_count = sum(1 for r in field_results if r['Correct'] == "TRUE")
            null_count = sum(1 for r in field_results if r['Correct'] == "NULL")
            total_count = len(field_results)
            comparable_count = total_count - null_count  # Only count non-NULL results for accuracy
            accuracy = round((correct_count / comparable_count * 100) if comparable_count > 0 else 0, 2)

            # Field name header
            ws.cell(row=stats_start_row, column=1, value=f"{field_name}:")
            ws.cell(row=stats_start_row, column=1).font = Font(bold=True)
            stats_start_row += 1

            # Statistics data
            wrong_count = total_count - correct_count - null_count
            stats = [
                ("Total Values", total_count),
                ("Correct", correct_count),
                ("Wrong", wrong_count),
                ("NULL (No CSV Match)", null_count),
                ("Accuracy (%)", accuracy)
            ]

            for stat_name, stat_value in stats:
                ws.cell(row=stats_start_row, column=1, value=f"  {stat_name}")
                ws.cell(row=stats_start_row, column=2, value=stat_value)
                stats_start_row += 1

            stats_start_row += 1  # Add space between fields

    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
        ws.column_dimensions[column_letter].width = adjusted_width

    # Save workbook
    wb.save(output_file)
    logger.info(f"Excel report saved to: {output_file}")

def find_extracted_files(extracted_dir, logger):
    """Find all extracted JSON files in the directory (not subfolders)."""
    extracted_files = []

    # Look for files directly in the extraction directory (not subfolders)
    pattern1 = os.path.join(extracted_dir, "*_output_result.json")
    pattern2 = os.path.join(extracted_dir, "*_extracted.json")
    pattern3 = os.path.join(extracted_dir, "*_*_extracted.json")  # New pattern for invoice_number_extracted.json
    pattern4 = os.path.join(extracted_dir, "*_*_extracted_v*.json")  # Pattern for versioned files

    extracted_files.extend(glob.glob(pattern1))
    extracted_files.extend(glob.glob(pattern2))
    extracted_files.extend(glob.glob(pattern3))
    extracted_files.extend(glob.glob(pattern4))

    # Remove duplicates
    extracted_files = list(set(extracted_files))

    # Filter out files in subdirectories
    direct_files = []
    for file_path in extracted_files:
        # Check if file is directly in the extraction directory
        if os.path.dirname(file_path) == extracted_dir:
            direct_files.append(file_path)

    logger.info(f"Found {len(direct_files)} extracted files in {extracted_dir}")
    return direct_files

def find_file_pairs(extracted_dir, true_dir, logger):
    """Find matching pairs of extracted and true data files."""
    extracted_files = glob.glob(os.path.join(extracted_dir, "*_output_result.json"))
    extracted_files += glob.glob(os.path.join(extracted_dir, "*_extracted.json"))
    extracted_files += glob.glob(os.path.join(extracted_dir, "*_*_extracted.json"))  # New pattern
    extracted_files += glob.glob(os.path.join(extracted_dir, "*_*_extracted_v*.json"))  # Versioned files

    # Remove duplicates
    extracted_files = list(set(extracted_files))
    file_pairs = []

    for extracted_file in extracted_files:
        # Extract base filename using smart extraction
        extracted_filename = os.path.basename(extracted_file)

        if extracted_filename.endswith("_output_result.json"):
            base_name = extracted_filename.replace("_output_result.json", "")
        else:
            # Use the new smart extraction function
            base_name = extract_original_filename_from_extracted(extracted_filename)

        true_file = os.path.join(true_dir, f"{base_name}_true_data.json")

        if os.path.exists(true_file):
            file_pairs.append((extracted_file, true_file, base_name))
            logger.info(f"Found pair: {base_name}")
        else:
            logger.warning(f"No true data file found for: {base_name}")

    return file_pairs

def main():
    """Main evaluation function."""
    parser = argparse.ArgumentParser(description='Evaluate extracted data against true data')
    parser.add_argument('--use-csv', action='store_true',
                       help='Use CSV file for true data instead of JSON files')
    parser.add_argument('--csv-file', '-c',
                       help='Path to CSV file containing true data (required when --use-csv is specified)')
    parser.add_argument('--extracted-dir', '-e', default='data/output_data/extraction',
                       help='Directory containing extracted result files')
    parser.add_argument('--true-dir', '-t', default='data/true_data/extraction',
                       help='Directory containing true data files (used when --use-csv is not specified)')
    parser.add_argument('--output-file', '-o', default='data/evaluation/evaluation_report.xlsx',
                       help='Output Excel file path')

    args = parser.parse_args()

    # Setup logging
    logger = setup_logging()
    logger.info("Starting evaluation process")

    # Ensure output directory exists (create evaluation folder)
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)

    # Evaluate all files
    all_results = {}

    if args.use_csv:
        # CSV evaluation mode
        if not args.csv_file:
            logger.error("CSV file path is required when using --use-csv option")
            return

        logger.info(f"Using CSV evaluation mode with file: {args.csv_file}")

        # Load CSV file
        csv_df = load_csv_file(args.csv_file)
        if csv_df is None:
            logger.error("Failed to load CSV file")
            return

        # Find extracted files
        extracted_files = find_extracted_files(args.extracted_dir, logger)

        if not extracted_files:
            logger.error("No extracted files found!")
            return

        # Evaluate each extracted file against CSV data
        for extracted_file in extracted_files:
            # Extract base filename for comparison using smart extraction
            extracted_filename = os.path.basename(extracted_file)

            if extracted_filename.endswith("_output_result.json"):
                base_name = extracted_filename.replace("_output_result.json", "")
            else:
                # Use the new smart extraction function for all other files
                base_name = extract_original_filename_from_extracted(extracted_filename)

            results = evaluate_single_file_with_csv(extracted_file, csv_df, base_name, logger)
            if results:
                all_results[base_name] = results

    else:
        # JSON evaluation mode (original functionality)
        logger.info("Using JSON evaluation mode")

        # Find file pairs
        file_pairs = find_file_pairs(args.extracted_dir, args.true_dir, logger)

        if not file_pairs:
            logger.error("No matching file pairs found!")
            return

        for extracted_file, true_file, base_name in file_pairs:
            results = evaluate_single_file(extracted_file, true_file, base_name, logger)
            if results:
                all_results[base_name] = results

    if not all_results:
        logger.error("No successful evaluations!")
        return

    # Create report (Excel or CSV depending on availability)
    if OPENPYXL_AVAILABLE:
        create_excel_report(all_results, args.output_file, logger)
    else:
        create_csv_report(all_results, args.output_file, logger)

    # Print summary - Column-wise
    logger.info("\n=== EVALUATION SUMMARY (BY COLUMN) ===")

    # Collect all field names
    all_field_names = set()
    for results in all_results.values():
        for result in results:
            all_field_names.add(result['Field Name'])

    # Sort field names for consistent ordering
    sorted_field_names = sorted(all_field_names)

    # Calculate and display statistics for each field/column
    for field_name in sorted_field_names:
        # Collect all results for this field across all files
        field_results = []
        for results in all_results.values():
            field_results.extend([r for r in results if r['Field Name'] == field_name])

        if field_results:  # Only process if we have results for this field
            total = len(field_results)
            correct = sum(1 for r in field_results if r['Correct'] == "TRUE")
            null_count = sum(1 for r in field_results if r['Correct'] == "NULL")
            comparable = total - null_count
            accuracy = (correct / comparable * 100) if comparable > 0 else 0
            logger.info(f"{field_name}: {correct}/{comparable} correct ({accuracy:.1f}%) | {null_count} NULL (no CSV match)")

def run_csv_evaluation(csv_file_path, extracted_dir="data/output_data/extraction", output_file=None):
    """
    Run CSV evaluation with direct function call (for VS Code terminal usage).

    Args:
        csv_file_path (str): Path to CSV file containing true data
        extracted_dir (str): Directory containing extracted JSON files
        output_file (str): Output Excel file path (optional, auto-generated if None)

    Returns:
        dict: Evaluation results summary
    """
    from datetime import datetime

    # Setup logging
    logger = setup_logging()
    logger.info("Starting CSV evaluation process")

    # Auto-generate output file if not provided
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"data/evaluation/csv_evaluation_{timestamp}.xlsx"

    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    logger.info(f"Using CSV evaluation mode with file: {csv_file_path}")

    # Load CSV file
    csv_df = load_csv_file(csv_file_path)
    if csv_df is None:
        logger.error("Failed to load CSV file")
        return None

    # Find extracted files
    extracted_files = find_extracted_files(extracted_dir, logger)

    if not extracted_files:
        logger.error("No extracted files found!")
        return None

    # Evaluate each extracted file against CSV data
    all_results = {}
    logger.info(f"Found {len(extracted_files)} extracted files to evaluate")

    for i, extracted_file in enumerate(extracted_files, 1):
        # Extract base filename for comparison using smart extraction
        extracted_filename = os.path.basename(extracted_file)
        logger.info(f"Processing file {i}/{len(extracted_files)}: {extracted_filename}")

        if extracted_filename.endswith("_output_result.json"):
            base_name = extracted_filename.replace("_output_result.json", "")
        else:
            # Use the new smart extraction function for all other files
            base_name = extract_original_filename_from_extracted(extracted_filename)

        logger.info(f"  Extracted base name: {base_name}")

        # Load the extracted file to get the invoice number for better logging
        extracted_data = load_json_file(extracted_file)
        if extracted_data:
            extracted_invoice_number = extracted_data.get('invoice_number', 'N/A')
            logger.info(f"  Invoice number in file: {extracted_invoice_number}")

        results = evaluate_single_file_with_csv(extracted_file, csv_df, base_name, logger)
        if results:
            all_results[base_name] = results
            logger.info(f"  ✅ Evaluation successful for {base_name}")
        else:
            logger.warning(f"  ❌ Evaluation failed for {base_name}")

    logger.info(f"Successfully evaluated {len(all_results)} out of {len(extracted_files)} files")

    if not all_results:
        logger.error("No successful evaluations!")
        return None

    # Create report
    if OPENPYXL_AVAILABLE:
        create_excel_report(all_results, output_file, logger)
    else:
        create_csv_report(all_results, output_file, logger)

    # Calculate summary statistics - Column-wise
    summary = {}
    logger.info("\n=== EVALUATION SUMMARY (BY COLUMN) ===")

    # Collect all field names
    all_field_names = set()
    for results in all_results.values():
        for result in results:
            all_field_names.add(result['Field Name'])

    # Sort field names for consistent ordering
    sorted_field_names = sorted(all_field_names)

    # Calculate and display statistics for each field/column
    for field_name in sorted_field_names:
        # Collect all results for this field across all files
        field_results = []
        for results in all_results.values():
            field_results.extend([r for r in results if r['Field Name'] == field_name])

        if field_results:  # Only process if we have results for this field
            total = len(field_results)
            correct = sum(1 for r in field_results if r['Correct'] == "TRUE")
            null_count = sum(1 for r in field_results if r['Correct'] == "NULL")
            comparable = total - null_count
            accuracy = (correct / comparable * 100) if comparable > 0 else 0

            summary[field_name] = {
                'total_values': total,
                'correct': correct,
                'null_count': null_count,
                'comparable': comparable,
                'accuracy': accuracy
            }

            logger.info(f"{field_name}: {correct}/{comparable} correct ({accuracy:.1f}%) | {null_count} NULL (no CSV match)")

    logger.info(f"\n📊 Report saved to: {output_file}")
    return {
        'output_file': output_file,
        'summary': summary,
        'total_files': len(all_results)
    }

def run_json_evaluation(extracted_dir="data/output_data/extraction", true_dir="data/true_data/extraction", output_file=None):
    """
    Run JSON evaluation with direct function call (for VS Code terminal usage).

    Args:
        extracted_dir (str): Directory containing extracted JSON files
        true_dir (str): Directory containing true data JSON files
        output_file (str): Output Excel file path (optional, auto-generated if None)

    Returns:
        dict: Evaluation results summary
    """
    from datetime import datetime

    # Setup logging
    logger = setup_logging()
    logger.info("Starting JSON evaluation process")

    # Auto-generate output file if not provided
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"data/evaluation/json_evaluation_{timestamp}.xlsx"

    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    logger.info("Using JSON evaluation mode")

    # Find file pairs
    file_pairs = find_file_pairs(extracted_dir, true_dir, logger)

    if not file_pairs:
        logger.error("No matching file pairs found!")
        return None

    # Evaluate all files
    all_results = {}
    for extracted_file, true_file, base_name in file_pairs:
        results = evaluate_single_file(extracted_file, true_file, base_name, logger)
        if results:
            all_results[base_name] = results

    if not all_results:
        logger.error("No successful evaluations!")
        return None

    # Create report
    if OPENPYXL_AVAILABLE:
        create_excel_report(all_results, output_file, logger)
    else:
        create_csv_report(all_results, output_file, logger)

    # Calculate summary statistics - Column-wise
    summary = {}
    logger.info("\n=== EVALUATION SUMMARY (BY COLUMN) ===")

    # Collect all field names
    all_field_names = set()
    for results in all_results.values():
        for result in results:
            all_field_names.add(result['Field Name'])

    # Sort field names for consistent ordering
    sorted_field_names = sorted(all_field_names)

    # Calculate and display statistics for each field/column
    for field_name in sorted_field_names:
        # Collect all results for this field across all files
        field_results = []
        for results in all_results.values():
            field_results.extend([r for r in results if r['Field Name'] == field_name])

        if field_results:  # Only process if we have results for this field
            total = len(field_results)
            correct = sum(1 for r in field_results if r['Correct'] == "TRUE")
            null_count = sum(1 for r in field_results if r['Correct'] == "NULL")
            comparable = total - null_count
            accuracy = (correct / comparable * 100) if comparable > 0 else 0

            summary[field_name] = {
                'total_values': total,
                'correct': correct,
                'null_count': null_count,
                'comparable': comparable,
                'accuracy': accuracy
            }

            logger.info(f"{field_name}: {correct}/{comparable} correct ({accuracy:.1f}%) | {null_count} NULL (no CSV match)")

    logger.info(f"\n📊 Report saved to: {output_file}")
    return {
        'output_file': output_file,
        'summary': summary,
        'total_files': len(all_results)
    }

if __name__ == "__main__":
    run_csv_evaluation(csv_file_path=r"/home/<USER>/Documents/repositories/logistically/docs/logistically_attachment_data_with_invoice_info.csv",
                       extracted_dir=r"/home/<USER>/Documents/repositories/logistically/data/output_data/extraction")