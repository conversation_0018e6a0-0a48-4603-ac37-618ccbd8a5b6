import os
import shutil
from pathlib import Path
import PyPDF2
from typing import List, Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_pdf_page_count(pdf_path: str) -> int:
    """
    Get the number of pages in a PDF file.

    Args:
        pdf_path (str): Path to the PDF file

    Returns:
        int: Number of pages in the PDF, or 0 if error
    """
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            return len(pdf_reader.pages)
    except Exception as e:
        logger.warning(f"Error reading PDF {pdf_path}: {e}")
        return 0

def get_file_size(file_path: str) -> int:
    """
    Get the size of a file in bytes.

    Args:
        file_path (str): Path to the file

    Returns:
        int: File size in bytes, or 0 if error
    """
    try:
        return os.path.getsize(file_path)
    except Exception as e:
        logger.warning(f"Error getting size of {file_path}: {e}")
        return 0

def select_representative_pdfs(pdf_files: List[Tuple[str, int]], target_count: int = 7) -> List[str]:
    """
    Select representative PDFs from a sorted list (by size, descending).
    Selection strategy: 1 highest, 1 lowest, and rest distributed between them.

    Args:
        pdf_files (List[Tuple[str, int]]): List of (file_path, file_size) tuples, sorted by size desc
        target_count (int): Number of files to select (default: 7)

    Returns:
        List[str]: List of selected file paths
    """
    if len(pdf_files) <= target_count:
        return [pdf[0] for pdf in pdf_files]

    selected = []

    # Add highest (first in sorted list)
    selected.append(pdf_files[0][0])

    # Add lowest (last in sorted list)
    selected.append(pdf_files[-1][0])

    # Calculate remaining slots
    remaining_slots = target_count - 2

    if remaining_slots > 0 and len(pdf_files) > 2:
        # Select from the middle range (excluding first and last)
        middle_files = pdf_files[1:-1]

        if len(middle_files) <= remaining_slots:
            # If we have fewer middle files than slots, take all
            selected.extend([pdf[0] for pdf in middle_files])
        else:
            # Distribute evenly across the middle range
            step = len(middle_files) / remaining_slots
            for i in range(remaining_slots):
                index = int(i * step)
                selected.append(middle_files[index][0])

    return selected

def shortlist_invoice_files():
    """
    Main function to shortlist invoice files based on the specified criteria:
    1. Read all vendor folders in the invoice directory
    2. For each vendor, filter PDFs with <= 2 pages
    3. Sort by file size (descending)
    4. Select 7 representative PDFs (1 highest, 1 lowest, 5 from between)
    5. Copy to invoice_shortlisted folder maintaining vendor structure
    """
    # Define paths
    base_path = Path("/home/<USER>/Documents/repositories/logistically")
    invoice_path = base_path / "data" / "input_data" / "10k_w_true_data" / "invoice"
    shortlisted_path = base_path / "data" / "input_data" / "10k_w_true_data" / "invoice_shortlisted_v2"

    # Create shortlisted directory if it doesn't exist
    shortlisted_path.mkdir(parents=True, exist_ok=True)

    logger.info(f"Processing invoice files from: {invoice_path}")
    logger.info(f"Output directory: {shortlisted_path}")

    # Get all vendor folders
    vendor_folders = [f for f in invoice_path.iterdir() if f.is_dir()]

    total_vendors = len(vendor_folders)
    processed_vendors = 0
    total_files_copied = 0

    for vendor_folder in vendor_folders:
        vendor_name = vendor_folder.name
        logger.info(f"Processing vendor: {vendor_name}")

        # Get all PDF files in vendor folder
        pdf_files = list(vendor_folder.glob("*.pdf"))

        if not pdf_files:
            logger.info(f"No PDF files found for vendor: {vendor_name}")
            continue

        # Filter PDFs with <= 2 pages and get their sizes
        eligible_pdfs = []

        for pdf_file in pdf_files:
            page_count = get_pdf_page_count(str(pdf_file))

            if page_count <= 2 and page_count > 0:  # Valid PDFs with <= 2 pages
                file_size = get_file_size(str(pdf_file))
                eligible_pdfs.append((str(pdf_file), file_size))

        if not eligible_pdfs:
            logger.info(f"No eligible PDFs (<=2 pages) found for vendor: {vendor_name}")
            continue

        # Sort by file size (descending)
        eligible_pdfs.sort(key=lambda x: x[1], reverse=True)

        logger.info(f"Found {len(eligible_pdfs)} eligible PDFs for {vendor_name}")

        # Select representative PDFs
        selected_pdfs = select_representative_pdfs(eligible_pdfs, target_count=3)

        # Create vendor folder in shortlisted directory
        vendor_shortlisted_path = shortlisted_path / vendor_name
        vendor_shortlisted_path.mkdir(parents=True, exist_ok=True)

        # Copy selected PDFs
        for pdf_path in selected_pdfs:
            pdf_file = Path(pdf_path)
            destination = vendor_shortlisted_path / pdf_file.name

            try:
                shutil.copy2(pdf_path, destination)
                logger.info(f"Copied: {pdf_file.name}")
                total_files_copied += 1
            except Exception as e:
                logger.error(f"Error copying {pdf_file.name}: {e}")

        processed_vendors += 1
        logger.info(f"Completed {vendor_name}: {len(selected_pdfs)} files copied")
        logger.info(f"Progress: {processed_vendors}/{total_vendors} vendors processed")

    logger.info(f"Shortlisting completed!")
    logger.info(f"Total vendors processed: {processed_vendors}")
    logger.info(f"Total files copied: {total_files_copied}")
    logger.info(f"Output location: {shortlisted_path}")

if __name__ == "__main__":
    shortlist_invoice_files()