import json
import time
import boto3
import os
import argparse
import logging
import glob

import shutil
from datetime import datetime
from pathlib import Path
from typing import Optional
from botocore.exceptions import ClientError

os.chdir(r"/home/<USER>/Documents/repositories/logistically")

def setup_logging():
    """Setup logging configuration."""
    # Create logs directory if it doesn't exist
    logs_dir = Path("data/logs")
    logs_dir.mkdir(parents=True, exist_ok=True)

    # Create log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = logs_dir / f"bedrock_automation_{timestamp}.log"

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # Also log to console
        ]
    )

    return logging.getLogger(__name__)

class BedrockDataAutomation:
    def __init__(self, region: str, bucket_name: str, project_id: str, temp_input_prefix: str, temp_output_prefix: str, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        # session = boto3.Session(profile_name="DeveloperLearningAccountAccess-************")
        self.bda_client = boto3.client('bedrock-data-automation-runtime', region_name=region)
        self.s3_client = boto3.client('s3', region_name=region)
        self.sts_client = boto3.client('sts')
        self.bucket_name = bucket_name
        self.temp_input_prefix = temp_input_prefix
        self.temp_output_prefix = temp_output_prefix
        self.project_id = project_id
        self.aws_account_id = self.get_aws_account_id()
        self.logger.info(f"Initialized BedrockDataAutomation for project {project_id} in region {region}")

    def get_aws_account_id(self) -> str:
        """Retrieve AWS account ID."""
        return self.sts_client.get_caller_identity().get('Account')

    def upload_file_to_s3(self, local_file_path: str) -> str:
        """Upload local file to S3 and return S3 URI."""
        try:
            file_name = os.path.basename(local_file_path)
            s3_key = f"{self.temp_input_prefix}/{file_name}"
            self.logger.info(f"Uploading {local_file_path} to s3://{self.bucket_name}/{s3_key}")
            self.s3_client.upload_file(local_file_path, self.bucket_name, s3_key)
            s3_uri = f"s3://{self.bucket_name}/{s3_key}"
            self.logger.info(f"Successfully uploaded to {s3_uri}")
            return s3_uri
        except ClientError as e:
            self.logger.error(f"Error uploading file to S3: {e}")
            raise

    def download_file_from_s3(self, s3_uri: str, local_output_path: str):
        """Download file from S3 to local path."""
        try:
            s3_key = '/'.join(s3_uri.split('/')[3:])
            self.logger.info(f"Downloading {s3_uri} to {local_output_path}")

            # Ensure output directory exists
            os.makedirs(os.path.dirname(local_output_path), exist_ok=True)

            self.s3_client.download_file(self.bucket_name, s3_key, local_output_path)
            self.logger.info(f"Successfully downloaded to {local_output_path}")

            with open(local_output_path, 'r') as f:
                return json.load(f)
        except ClientError as e:
            self.logger.error(f"Error downloading file from S3: {e}")
            raise
        except json.JSONDecodeError as e:
            self.logger.error(f"Error parsing JSON from downloaded file: {e}")
            raise

    def invoke_data_automation(self, input_s3_uri: str, output_s3_uri: str) -> dict:
        """Invoke Bedrock Data Automation async processing."""
        try:
            data_automation_arn = f"arn:aws:bedrock:{self.bda_client.meta.region_name}:{self.aws_account_id}:data-automation-project/{self.project_id}"
            # Updated profile ARN - replace with the correct one from your AWS account
            profile_arn = f"arn:aws:bedrock:us-east-1:************:data-automation-profile/us.data-automation-v1"

            self.logger.info(f"Invoking BDA for input: {input_s3_uri}")
            self.logger.info(f"Output will be saved to: {output_s3_uri}")

            response = self.bda_client.invoke_data_automation_async(
                inputConfiguration={'s3Uri': input_s3_uri},
                outputConfiguration={'s3Uri': output_s3_uri},
                # dataAutomationConfiguration={
                #     'dataAutomationProjectArn': data_automation_arn,
                #     'stage': 'LIVE'  # Updated to 'LIVE' - verify and adjust based on your project
                # },
                dataAutomationProfileArn=profile_arn,
                blueprints=[
                    {
                        "blueprintArn": "arn:aws:bedrock:us-east-1:aws:blueprint/bedrock-data-automation-public-invoice"
                #         "stage": "LIVE",
                #         "version": "1.0"
                    }
                ]
            )

            invocation_arn = response.get('invocationArn')
            self.logger.info(f"BDA invocation started with ARN: {invocation_arn}")
            return response
        except ClientError as e:
            self.logger.error(f"Error invoking BDA: {e}")
            raise

    def check_job_status(self, invocation_arn: str) -> dict:
        """Check the status of the BDA processing job."""
        try:
            self.logger.info(f"Monitoring job status for ARN: {invocation_arn}")
            while True:
                status_response = self.bda_client.get_data_automation_status(invocationArn=invocation_arn)
                status = status_response.get('status')
                self.logger.info(f"Job status: {status}")

                if status in ['Success', 'Failed']:
                    self.logger.info(f"Job completed with status: {status}")
                    return status_response
                time.sleep(5)
        except ClientError as e:
            self.logger.error(f"Error checking job status: {e}")
            raise

    def move_processed_file(self, input_file_path: str) -> str:
        """Move successfully processed file to processed folder."""
        try:
            # Create processed folder if it doesn't exist
            input_dir = os.path.dirname(input_file_path)
            processed_dir = os.path.join(input_dir, "processed")
            os.makedirs(processed_dir, exist_ok=True)

            # Generate destination path
            file_name = os.path.basename(input_file_path)
            destination_path = os.path.join(processed_dir, file_name)

            # Move the file
            shutil.move(input_file_path, destination_path)
            self.logger.info(f"Moved processed file: {input_file_path} -> {destination_path}")

            return destination_path
        except Exception as e:
            self.logger.error(f"Error moving processed file {input_file_path}: {e}")
            raise

    def process_single_file(self, input_file_path: str, output_dir: str = "data/output_data/extraction") -> dict:
        """Process a single file and return results."""
        try:
            # Generate output filename with _output suffix
            file_name = os.path.basename(input_file_path)
            name_without_ext = os.path.splitext(file_name)[0]
            output_filename = f"{name_without_ext}_output.json"
            output_file_path = os.path.join(output_dir, output_filename)

            self.logger.info(f"Processing file: {input_file_path}")

            # Upload file to S3
            input_s3_uri = self.upload_file_to_s3(input_file_path)
            output_s3_uri = f"s3://{self.bucket_name}/{self.temp_output_prefix}/"

            # Invoke BDA processing
            response = self.invoke_data_automation(input_s3_uri, output_s3_uri)
            invocation_arn = response.get('invocationArn')

            if not invocation_arn:
                raise ValueError("No invocation ARN returned from BDA service")

            # Check job status
            status_response = self.check_job_status(invocation_arn)

            if status_response.get('status') == 'Success':
                # Get output S3 URI from the response
                output_s3_uri = status_response.get('outputS3Uri') or status_response.get('outputConfiguration', {}).get('s3Uri')

                if output_s3_uri:
                    # Generate metadata file path in logs folder
                    file_name = os.path.basename(input_file_path)
                    name_without_ext = os.path.splitext(file_name)[0]
                    metadata_filename = f"{name_without_ext}_output.json"
                    metadata_file_path = os.path.join("data/logs", metadata_filename)

                    # First download the job metadata to get the actual result paths
                    metadata = self.download_file_from_s3(output_s3_uri, metadata_file_path)

                    # Extract the actual result file paths from metadata
                    if 'output_metadata' in metadata and metadata['output_metadata']:
                        asset_metadata = metadata['output_metadata'][0]
                        if 'segment_metadata' in asset_metadata and asset_metadata['segment_metadata']:
                            segment = asset_metadata['segment_metadata'][0]

                            # Try to get the custom output first, then standard output
                            result_s3_uri = segment.get('custom_output_path') or segment.get('standard_output_path')

                            if result_s3_uri:
                                # Download the actual extracted data to output_data/extraction
                                result_file_path = output_file_path.replace('.json', '_result.json')
                                actual_results = self.download_file_from_s3(result_s3_uri, result_file_path)

                                self.logger.info(f"Successfully processed {input_file_path}")
                                self.logger.info(f"  - Extracted data: {result_file_path}")
                                self.logger.info(f"  - Metadata: {metadata_file_path}")

                                # Move the processed file to processed folder
                                try:
                                    moved_file_path = self.move_processed_file(input_file_path)
                                    return {
                                        'status': 'success',
                                        'input_file': input_file_path,
                                        'moved_to': moved_file_path,
                                        'output_file': result_file_path,
                                        'metadata_file': metadata_file_path,
                                        'results': actual_results
                                    }
                                except Exception as move_error:
                                    self.logger.warning(f"File processed successfully but failed to move: {move_error}")
                                    return {
                                        'status': 'success',
                                        'input_file': input_file_path,
                                        'output_file': result_file_path,
                                        'metadata_file': metadata_file_path,
                                        'results': actual_results,
                                        'move_error': str(move_error)
                                    }

                    # Fallback to metadata if we can't extract result paths
                    self.logger.info(f"Successfully processed {input_file_path} -> {metadata_file_path} (metadata only)")

                    # Move the processed file to processed folder
                    try:
                        moved_file_path = self.move_processed_file(input_file_path)
                        return {
                            'status': 'success',
                            'input_file': input_file_path,
                            'moved_to': moved_file_path,
                            'output_file': metadata_file_path,
                            'results': metadata
                        }
                    except Exception as move_error:
                        self.logger.warning(f"File processed successfully but failed to move: {move_error}")
                        return {
                            'status': 'success',
                            'input_file': input_file_path,
                            'output_file': metadata_file_path,
                            'results': metadata,
                            'move_error': str(move_error)
                        }
                else:
                    self.logger.error("No output S3 URI found in status response")
                    return {
                        'status': 'error',
                        'input_file': input_file_path,
                        'error': 'No output S3 URI found'
                    }
            else:
                self.logger.error(f"Processing failed for {input_file_path}")
                return {
                    'status': 'failed',
                    'input_file': input_file_path,
                    'error': 'BDA processing failed'
                }

        except Exception as e:
            self.logger.error(f"Error processing file {input_file_path}: {e}")
            return {
                'status': 'error',
                'input_file': input_file_path,
                'error': str(e)
            }

    def process_multiple_files(self, input_folder: str, max_files: Optional[int] = None, output_dir: str = "data/output_data/extraction") -> list:
        """Process multiple files from a folder."""
        # Get all supported file types (PDF, images, etc.)
        supported_extensions = ['*.pdf', '*.png', '*.jpg', '*.jpeg', '*.tiff', '*.bmp']
        input_files = []

        for ext in supported_extensions:
            input_files.extend(glob.glob(os.path.join(input_folder, ext)))
            input_files.extend(glob.glob(os.path.join(input_folder, ext.upper())))

        if not input_files:
            self.logger.warning(f"No supported files found in {input_folder}")
            return []

        # Limit number of files if specified
        if max_files and max_files > 0:
            input_files = input_files[:max_files]
            self.logger.info(f"Processing {len(input_files)} files (limited to {max_files})")
        else:
            self.logger.info(f"Processing all {len(input_files)} files found")

        results = []
        for i, file_path in enumerate(input_files, 1):
            self.logger.info(f"Processing file {i}/{len(input_files)}: {file_path}")
            result = self.process_single_file(file_path, output_dir)
            results.append(result)

            # Add a small delay between files to avoid overwhelming the service
            if i < len(input_files):
                time.sleep(2)

        return results

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Bedrock Data Automation Batch Processor')
    parser.add_argument('--input-folder', '-i',
                       help='Path to folder containing input files to process')
    parser.add_argument('--max-files', '-m', type=int, default=None,
                       help='Maximum number of files to process (default: process all)')
    parser.add_argument('--output-dir', '-o', default='data/output_data/extraction',
                       help='Output directory for processed files (default: data/output_data/extraction)')
    parser.add_argument('--single-file', '-s',
                       help='Process a single file instead of a folder')

    # Check if running from VS Code or without arguments
    import sys
    if len(sys.argv) == 1:
        # No arguments provided - use defaults for VS Code execution
        print("No arguments provided. Using default settings for VS Code execution:")
        print("  - Processing 1 file from data/input_data/extraction folder")
        print("  - Output will be saved to data/output_data/extraction")
        print("  - Use --help to see all available options")
        print()

        # Create a mock args object with default values
        class DefaultArgs:
            def __init__(self):
                self.input_folder = 'data/input_data/extraction'
                self.max_files = 15  # Process only 1 file by default
                self.output_dir = 'data/output_data/extraction'
                self.single_file = None

        return DefaultArgs()

    args = parser.parse_args()

    # Validate that either input-folder or single-file is provided
    if not args.input_folder and not args.single_file:
        parser.error("Either --input-folder or --single-file must be provided")

    return args

def main():
    # Parse command line arguments
    args = parse_arguments()

    # Setup logging
    logger = setup_logging()
    logger.info("Starting Bedrock Data Automation processing")

    # Configuration
    AWS_REGION = 'us-east-1'  # Ensure region supports BDA (us-east-1 or us-west-2)
    BUCKET_NAME = 'document-extraction-logistically'  # Replace with your S3 bucket
    PROJECT_ID = 'ad4a57a0c572'  # Verify this ID exists in your account
    TEMP_INPUT_PREFIX = 'temp/input'  # Temporary S3 input path
    TEMP_OUTPUT_PREFIX = 'temp/output'  # Temporary S3 output path

    # Initialize BDA client
    bda = BedrockDataAutomation(
        region=AWS_REGION,
        bucket_name=BUCKET_NAME,
        project_id=PROJECT_ID,
        temp_input_prefix=TEMP_INPUT_PREFIX,
        temp_output_prefix=TEMP_OUTPUT_PREFIX,
        logger=logger
    )

    try:
        if args.single_file:

            # Process single file
            if not os.path.exists(args.single_file):
                logger.error(f"Input file {args.single_file} not found.")
                return

            logger.info(f"Processing single file: {args.single_file}")
            result = bda.process_single_file(args.single_file, args.output_dir)

            if result['status'] == 'success':
                logger.info("Processing completed successfully!")
                logger.info(f"Results saved to: {result['output_file']}")
                print("\nExtracted Data:")
                print(json.dumps(result['results'], indent=2))
            else:
                logger.error(f"Processing failed: {result.get('error', 'Unknown error')}")

        else:
            # Process multiple files from folder
            if not os.path.exists(args.input_folder):
                logger.error(f"Input folder {args.input_folder} not found.")
                return

            logger.info(f"Processing files from folder: {args.input_folder}")
            results = bda.process_multiple_files(args.input_folder, args.max_files, args.output_dir)

            # Summary
            successful = sum(1 for r in results if r['status'] == 'success')
            failed = len(results) - successful

            logger.info(f"\nProcessing Summary:")
            logger.info(f"Total files processed: {len(results)}")
            logger.info(f"Successful: {successful}")
            logger.info(f"Failed: {failed}")

            if failed > 0:
                logger.info("\nFailed files:")
                for result in results:
                    if result['status'] != 'success':
                        logger.error(f"  {result['input_file']}: {result.get('error', 'Unknown error')}")

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise

if __name__ == "__main__":
    # command to process single file
    # python3 app/app_extraction_bda.py --single-file /home/<USER>/Documents/repositories/logistically/CTECH11379561_carrier_inv.pdf
    main()