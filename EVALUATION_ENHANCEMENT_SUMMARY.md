# Evaluation Extraction Enhancement Summary

## Overview
The `app/evaluation_extraction.py` script has been enhanced to support CSV-based evaluation in addition to the original JSON-based evaluation mode.

## New Features

### 1. CSV Evaluation Mode
- **New argument**: `--use-csv` - Enables CSV evaluation mode
- **New argument**: `--csv-file` - Path to CSV file containing true data (required when using `--use-csv`)

### 2. Enhanced Field Mapping
The script now evaluates all the required fields from the CSV file:
- `original_file_name`
- `invoice_number`
- `invoice_date`
- `invoice_amount`
- `vendor_name`
- `remit_to_name`
- `remit_to_address1`
- `remit_to_address_2`
- `remit_to_city`
- `remit_to_state_province`
- `remit_to_postal_code`
- `remit_to_country`

### 3. Smart Matching Logic
The script uses intelligent matching to find corresponding CSV rows:
1. **Primary matching**: By `invoice_number` from extracted JSON data
2. **Fallback matching**: By `original_file_name` (partial match, case-insensitive)

### 4. Directory Scanning
- Scans only files directly in the extraction directory (not subfolders)
- Supports both `*_extracted.json` and `*_output_result.json` file patterns

## Usage Examples

### CSV Evaluation Mode (New)
```bash
python app/evaluation_extraction.py \
    --use-csv \
    --csv-file "docs/logistically_attachment_data_with_invoice_info.csv" \
    --extracted-dir "data/output_data/extraction" \
    --output-file "data/evaluation/csv_evaluation_report.xlsx"
```

### JSON Evaluation Mode (Original)
```bash
python app/evaluation_extraction.py \
    --extracted-dir "data/output_data/extraction" \
    --true-dir "data/true_data/extraction" \
    --output-file "data/evaluation/json_evaluation_report.xlsx"
```

## Testing
A test script `test_evaluation_csv.py` has been created to demonstrate both modes:
```bash
python test_evaluation_csv.py
```

## File Structure Changes

### Modified Files
- `app/evaluation_extraction.py` - Enhanced with CSV evaluation functionality

### New Files
- `test_evaluation_csv.py` - Test script demonstrating both evaluation modes
- `test_null_functionality.py` - Test script demonstrating NULL handling for missing CSV matches
- `test_final_verification.py` - Final verification test for NULL functionality
- `EVALUATION_ENHANCEMENT_SUMMARY.md` - This documentation

### Dependencies Added
- `pandas` - For CSV file handling (already available in the environment)

## Key Functions Added

### `load_csv_file(csv_file_path)`
Loads and parses the CSV file containing true data.

### `find_csv_match_by_invoice_number(df, invoice_number, original_file_name)`
Finds matching rows in CSV by invoice number or original file name.

### `evaluate_single_file_with_csv(extracted_file, csv_df, file_name, logger)`
Evaluates extracted JSON data against CSV true data.

### `find_extracted_files(extracted_dir, logger)`
Finds extracted JSON files directly in the specified directory (excludes subfolders).

## Output
The script generates Excel reports with:
- Field-by-field comparison results
- Accuracy statistics per file
- Color-coded correct/incorrect indicators
- Summary statistics

## NULL Handling for Missing CSV Matches
When an extracted file's invoice number is not found in the CSV file:
- All field comparisons return "NULL" instead of TRUE/FALSE
- Excel report colors these rows **light blue** (#ADD8E6)
- Statistics exclude NULL values from accuracy calculations
- Summary shows separate counts: "X/Y correct (Z%) | N NULL (no CSV match)"

## Test Results
The CSV evaluation mode successfully processed extracted files against the CSV true data:
- Files with matching CSV data: accuracy rates ranging from 0% to 54.5%
- Files without CSV matches: show "NULL" values with light blue coloring
- **Confirmed NULL files**: FW-Carrier Invoice, 11474960CI, 8e2401e4-c332-4a7d-99bb-5430ba0a3597, MSINTL11325563_carrier_invoice
- NULL functionality verified with strict invoice number matching (no filename fallback)

## Backward Compatibility
The original JSON evaluation mode remains fully functional and unchanged. The new CSV mode is opt-in via the `--use-csv` flag.
