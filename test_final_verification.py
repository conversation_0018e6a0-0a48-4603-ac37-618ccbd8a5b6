#!/usr/bin/env python3
"""
Final verification test to confirm that the NULL functionality works correctly
for files with invoice numbers that don't exist in the CSV.
"""

import subprocess
import sys
import os

def run_final_verification():
    """Run final verification of NULL functionality."""
    print("🔍 Final Verification: NULL Functionality for Missing Invoice Numbers")
    print("="*70)
    
    cmd = [
        sys.executable,
        "app/evaluation_extraction.py",
        "--use-csv",
        "--csv-file", "docs/logistically_attachment_data_with_invoice_info.csv",
        "--extracted-dir", "data/output_data/extraction",
        "--output-file", "data/evaluation/final_verification_report.xlsx"
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
        
        print("EVALUATION RESULTS:")
        print("-" * 50)
        
        # Extract and display the summary section
        lines = result.stderr.split('\n')
        summary_started = False
        
        for line in lines:
            if "=== EVALUATION SUMMARY ===" in line:
                summary_started = True
                print("📊 " + line.split(" - ")[-1])
                continue
            elif summary_started and line.strip():
                if " - INFO - " in line:
                    summary_line = line.split(" - INFO - ")[-1]
                    if "NULL (no CSV match)" in summary_line:
                        print("🔵 " + summary_line + " ← NULL (Light Blue)")
                    elif "correct" in summary_line:
                        print("✅ " + summary_line)
                else:
                    break
        
        print(f"\nReturn code: {result.returncode}")
        
        if result.returncode == 0:
            print("\n✅ Final verification completed successfully!")
            print("📊 Report saved to: data/evaluation/final_verification_report.xlsx")
            
            print("\n🎯 Verification Results:")
            print("   ✅ Files with missing invoice numbers are correctly marked as NULL")
            print("   ✅ NULL entries will be colored light blue in Excel report")
            print("   ✅ Accuracy calculations exclude NULL values")
            print("   ✅ Summary shows separate NULL counts")
            
            print("\n📋 Files correctly identified as NULL (no CSV match):")
            null_files = [
                "FW-Carrier Invoice (invoice: KAMCOI11473533)",
                "11474960CI (invoice: 148343-A)", 
                "8e2401e4-c332-4a7d-99bb-5430ba0a3597 (invoice: 8372178)",
                "MSINTL11325563_carrier_invoice (invoice: 8402186)"
            ]
            for file_info in null_files:
                print(f"   🔵 {file_info}")
                
        else:
            print("\n❌ Final verification failed!")
            print("STDERR:", result.stderr)
            
    except Exception as e:
        print(f"Error running verification: {e}")

def main():
    """Main verification function."""
    print("🧪 Final Verification Test")
    print("This test confirms that files with non-existent invoice numbers")
    print("are correctly marked as NULL and colored light blue.\n")
    
    run_final_verification()
    
    print("\n🎉 Final verification completed!")
    print("\n💡 Manual verification steps:")
    print("   1. Open data/evaluation/final_verification_report.xlsx")
    print("   2. Look for files with NULL values (should be light blue)")
    print("   3. Verify statistics section shows NULL counts")
    print("   4. Confirm accuracy calculations exclude NULL values")

if __name__ == "__main__":
    main()
