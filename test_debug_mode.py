#!/usr/bin/env python3
"""
Test script to demonstrate debug mode functionality.
This shows how the debugger will now stop at exception points when debug_mode=True.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import from the file with + in the name
import importlib.util
spec = importlib.util.spec_from_file_location("textract_module", "app/app_ext_textract+llm.py")
textract_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(textract_module)

TextractBedrockAutomation = textract_module.TextractBedrockAutomation
setup_logging = textract_module.setup_logging

def test_debug_mode():
    """Test the debug mode functionality."""
    print("Testing debug mode functionality...")
    
    # Setup logging
    logger = setup_logging()
    
    # Configuration
    AWS_REGION = 'us-east-1'
    BUCKET_NAME = 'document-extraction-logistically'
    TEMP_INPUT_PREFIX = 'temp/textract_input'
    TEMP_OUTPUT_PREFIX = 'temp/textract_output'
    
    # Test with debug_mode=True
    print("\n1. Testing with debug_mode=True (will stop at exceptions):")
    processor_debug = TextractBedrockAutomation(
        region=AWS_REGION,
        bucket_name=BUCKET_NAME,
        temp_input_prefix=TEMP_INPUT_PREFIX,
        temp_output_prefix=TEMP_OUTPUT_PREFIX,
        logger=logger,
        debug_mode=True  # This will cause debugger to stop at exceptions
    )
    
    # Test with debug_mode=False
    print("\n2. Testing with debug_mode=False (normal exception handling):")
    processor_normal = TextractBedrockAutomation(
        region=AWS_REGION,
        bucket_name=BUCKET_NAME,
        temp_input_prefix=TEMP_INPUT_PREFIX,
        temp_output_prefix=TEMP_OUTPUT_PREFIX,
        logger=logger,
        debug_mode=False  # Normal exception handling
    )
    
    print("\nDebug mode setup complete!")
    print("When you run your actual code with debug_mode=True, the debugger will stop at exception points.")
    print("You can then inspect variables, step through code, etc.")
    
    return processor_debug, processor_normal

if __name__ == "__main__":
    test_debug_mode()
