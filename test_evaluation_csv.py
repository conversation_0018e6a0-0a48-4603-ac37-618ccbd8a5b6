#!/usr/bin/env python3
"""
Test script to demonstrate the new CSV evaluation functionality.
"""

import subprocess
import sys
import os

def run_csv_evaluation():
    """Run evaluation using CSV file for true data."""
    print("=" * 60)
    print("Testing CSV Evaluation Mode")
    print("=" * 60)
    
    cmd = [
        sys.executable, 
        "app/evaluation_extraction.py",
        "--use-csv",
        "--csv-file", "docs/logistically_attachment_data_with_invoice_info.csv",
        "--extracted-dir", "data/output_data/extraction",
        "--output-file", "data/evaluation/test_csv_evaluation_report.xlsx"
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        print(f"Return code: {result.returncode}")
        
        if result.returncode == 0:
            print("\n✅ CSV evaluation completed successfully!")
            print("📊 Report saved to: data/evaluation/test_csv_evaluation_report.xlsx")
        else:
            print("\n❌ CSV evaluation failed!")
            
    except Exception as e:
        print(f"Error running evaluation: {e}")

def run_json_evaluation():
    """Run evaluation using JSON files for true data (original mode)."""
    print("=" * 60)
    print("Testing JSON Evaluation Mode (Original)")
    print("=" * 60)
    
    cmd = [
        sys.executable, 
        "app/evaluation_extraction.py",
        "--extracted-dir", "data/output_data/extraction",
        "--true-dir", "data/true_data/extraction",
        "--output-file", "data/evaluation/test_json_evaluation_report.xlsx"
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        print(f"Return code: {result.returncode}")
        
        if result.returncode == 0:
            print("\n✅ JSON evaluation completed successfully!")
            print("📊 Report saved to: data/evaluation/test_json_evaluation_report.xlsx")
        else:
            print("\n❌ JSON evaluation failed!")
            
    except Exception as e:
        print(f"Error running evaluation: {e}")

if __name__ == "__main__":
    print("🧪 Testing Enhanced Evaluation Script")
    print("This script tests both CSV and JSON evaluation modes.\n")
    
    # Test CSV evaluation
    run_csv_evaluation()
    
    print("\n" + "=" * 60)
    print()
    
    # Test JSON evaluation (if true data directory exists)
    if os.path.exists("data/true_data/extraction"):
        run_json_evaluation()
    else:
        print("⚠️  Skipping JSON evaluation test - true data directory not found")
        print("   (This is expected if you only have CSV true data)")
    
    print("\n🎉 Testing completed!")
