#!/usr/bin/env python3
"""
Test script to verify that every extracted JSON file has an invoice number in the filename.
This script tests the enhanced filename generation and evaluation matching.
"""

import sys
import os
import json
import re
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def test_filename_format_enforcement():
    """Test that all extracted files have invoice numbers in their filenames."""
    
    print("🧪 Testing Invoice Number Enforcement in Filenames")
    print("=" * 60)
    
    # Check the output directory for extracted files
    output_dir = "data/output_data/extraction"
    
    if not os.path.exists(output_dir):
        print(f"❌ Output directory not found: {output_dir}")
        return False
    
    # Find all extracted JSON files
    extracted_files = []
    for file in os.listdir(output_dir):
        if file.endswith("_extracted.json") and os.path.isfile(os.path.join(output_dir, file)):
            extracted_files.append(file)
    
    if not extracted_files:
        print("❌ No extracted files found to test")
        return False
    
    print(f"Found {len(extracted_files)} extracted files to analyze")
    print()
    
    # Analyze each file
    files_with_invoice_numbers = 0
    files_without_invoice_numbers = 0
    
    for filename in extracted_files:
        # Check if filename has invoice number pattern
        # Pattern: name_invoicenum_extracted.json
        base_name = filename.replace("_extracted.json", "")
        parts = base_name.split("_")
        
        # Check if the last part (before _extracted) looks like an invoice number
        has_invoice_number = False
        if len(parts) >= 2:
            last_part = parts[-1]
            # Invoice number should contain digits or be alphanumeric
            if re.match(r'^[A-Za-z0-9_-]+$', last_part) and any(c.isdigit() for c in last_part):
                has_invoice_number = True
        
        # Also check the actual JSON content
        file_path = os.path.join(output_dir, filename)
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                actual_invoice_number = data.get('invoice_number', '')
        except:
            actual_invoice_number = ''
        
        if has_invoice_number:
            files_with_invoice_numbers += 1
            status = "✅"
        else:
            files_without_invoice_numbers += 1
            status = "❌"
        
        print(f"{status} {filename}")
        print(f"    Invoice in filename: {'Yes' if has_invoice_number else 'No'}")
        print(f"    Invoice in JSON: {actual_invoice_number or 'None'}")
        print()
    
    print("=" * 60)
    print(f"📊 RESULTS:")
    print(f"   Files with invoice numbers in filename: {files_with_invoice_numbers}")
    print(f"   Files without invoice numbers in filename: {files_without_invoice_numbers}")
    print(f"   Total files: {len(extracted_files)}")
    
    success_rate = (files_with_invoice_numbers / len(extracted_files)) * 100
    print(f"   Success rate: {success_rate:.1f}%")
    
    if files_without_invoice_numbers == 0:
        print("\n🎉 All files have invoice numbers in their filenames!")
        return True
    else:
        print(f"\n⚠️  {files_without_invoice_numbers} files are missing invoice numbers in filenames")
        return False

def test_filename_extraction_with_invoice_numbers():
    """Test that the evaluation script correctly extracts original filenames."""
    
    print("\n🔍 Testing Filename Extraction with Invoice Numbers")
    print("=" * 60)
    
    try:
        from evaluation_extraction import extract_original_filename_from_extracted
    except ImportError:
        print("❌ Could not import evaluation_extraction module")
        return False
    
    # Test cases with invoice numbers
    test_cases = [
        ("invoice_12345_extracted.json", "invoice", "Simple with invoice number"),
        ("complex_name_ABC123_extracted.json", "complex_name", "Complex name with invoice"),
        ("file_NOINV_123456_extracted.json", "file", "Fallback invoice number"),
        ("vendor_bill_INV-001_extracted.json", "vendor_bill", "Special chars in invoice"),
        ("test_67890_extracted_v2.json", "test", "With version number"),
    ]
    
    all_passed = True
    
    for input_filename, expected_output, description in test_cases:
        result = extract_original_filename_from_extracted(input_filename)
        passed = result == expected_output
        all_passed = all_passed and passed
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {description}")
        print(f"    Input:    {input_filename}")
        print(f"    Expected: {expected_output}")
        print(f"    Got:      {result}")
        print()
    
    return all_passed

def test_evaluation_matching():
    """Test that the evaluation script can find and match files correctly."""
    
    print("\n🎯 Testing Evaluation File Matching")
    print("=" * 60)
    
    try:
        from evaluation_extraction import find_extracted_files, extract_original_filename_from_extracted
    except ImportError:
        print("❌ Could not import evaluation_extraction module")
        return False
    
    # Mock logger
    class MockLogger:
        def info(self, msg): pass
        def warning(self, msg): pass
        def error(self, msg): pass
    
    logger = MockLogger()
    
    # Test finding extracted files
    output_dir = "data/output_data/extraction"
    if os.path.exists(output_dir):
        extracted_files = find_extracted_files(output_dir, logger)
        print(f"Found {len(extracted_files)} extracted files")
        
        # Test filename extraction for each file
        successful_extractions = 0
        for file_path in extracted_files[:5]:  # Test first 5 files
            filename = os.path.basename(file_path)
            try:
                original_name = extract_original_filename_from_extracted(filename)
                print(f"✅ {filename} → {original_name}")
                successful_extractions += 1
            except Exception as e:
                print(f"❌ {filename} → Error: {e}")
        
        success_rate = (successful_extractions / min(len(extracted_files), 5)) * 100
        print(f"\nExtraction success rate: {success_rate:.1f}%")
        
        return success_rate == 100.0
    else:
        print(f"❌ Output directory not found: {output_dir}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Enhanced Invoice Number Enforcement")
    print("=" * 80)
    
    # Test 1: Filename format enforcement
    test1_passed = test_filename_format_enforcement()
    
    # Test 2: Filename extraction with invoice numbers
    test2_passed = test_filename_extraction_with_invoice_numbers()
    
    # Test 3: Evaluation matching
    test3_passed = test_evaluation_matching()
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 TEST SUMMARY:")
    print(f"   Filename format enforcement: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"   Filename extraction: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    print(f"   Evaluation matching: {'✅ PASS' if test3_passed else '❌ FAIL'}")
    
    all_passed = test1_passed and test2_passed and test3_passed
    
    if all_passed:
        print("\n🎉 All tests passed! Invoice number enforcement is working correctly.")
        print("\n📌 Key Benefits:")
        print("   • Every extracted file has an invoice number in filename")
        print("   • Fallback invoice numbers generated when none found")
        print("   • Evaluation script correctly matches files by invoice number")
        print("   • No file overwrites possible")
    else:
        print("\n⚠️  Some tests failed. Please review the implementation.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
