# Smart Filename Handling Solution

## Problem Solved

**Issue**: When processing multiple files with the same name (e.g., `invoice.pdf` from different folders), the extraction script was generating the same output filename (`invoice_extracted.json`), causing files to overwrite each other. This resulted in only 2-3 files being visible in the output even when 5 files were processed.

## Solution Overview

Implemented a **smart filename generation and extraction system** that:

1. **Prevents overwrites** by including invoice numbers in filenames
2. **Handles edge cases** with automatic versioning
3. **Maintains compatibility** with the evaluation script
4. **Preserves original filename mapping** for accurate comparison

## Key Changes Made

### 1. Enhanced Extraction Script (`app_ext_textract_llm.py`)

#### Smart Filename Generation:
```python
# Before: invoice_extracted.json (overwrites)
# After:  invoice_12345_extracted.json (unique)

invoice_number = extracted_invoice_data.get('invoice_number', '')
if invoice_number and str(invoice_number).strip():
    # Clean special characters: ABC/123 -> ABC_123
    clean_invoice_number = re.sub(r'[^\w\-]', '_', str(invoice_number).strip())
    output_filename = f"{name_without_ext}_{clean_invoice_number}_extracted.json"
else:
    output_filename = f"{name_without_ext}_extracted.json"
```

#### Automatic Versioning:
```python
# If file still exists, add version number
counter = 1
while os.path.exists(output_file_path):
    base_name = os.path.splitext(original_output_path)[0]
    output_file_path = f"{base_name}_v{counter}.json"
    counter += 1
```

### 2. Enhanced Evaluation Script (`evaluation_extraction.py`)

#### Smart Filename Extraction:
```python
def extract_original_filename_from_extracted(extracted_filename):
    """
    Extracts original filename from various formats:
    - invoice_12345_extracted.json -> invoice
    - invoice_ABC_123_extracted_v2.json -> invoice
    - complex_name_INV001_extracted.json -> complex_name
    """
```

#### Updated File Discovery:
```python
# Now finds all these patterns:
pattern1 = "*_output_result.json"
pattern2 = "*_extracted.json"
pattern3 = "*_*_extracted.json"      # With invoice numbers
pattern4 = "*_*_extracted_v*.json"   # With versions
```

## Filename Examples

### Generation Examples:
| Original File | Invoice Number | Generated Filename |
|---------------|----------------|-------------------|
| `invoice.pdf` | `12345` | `invoice_12345_extracted.json` |
| `invoice.pdf` | `ABC-123` | `invoice_ABC_123_extracted.json` |
| `bill.pdf` | `INV/001` | `bill_INV_001_extracted.json` |
| `invoice.pdf` | *(empty)* | `invoice_extracted.json` |

### Extraction Examples:
| Extracted Filename | Original Name Extracted |
|-------------------|------------------------|
| `invoice_12345_extracted.json` | `invoice` |
| `invoice_ABC_123_extracted_v2.json` | `invoice` |
| `complex_name_INV001_extracted.json` | `complex_name` |
| `simple_extracted.json` | `simple` |

## Benefits

### ✅ **No More Overwrites**
- Each file gets a unique name based on its invoice number
- Automatic versioning prevents any remaining conflicts
- All processed files are preserved

### ✅ **Accurate Evaluation**
- Evaluation script correctly maps extracted files to CSV data
- Original filename is preserved for comparison
- Works with both CSV and JSON evaluation modes

### ✅ **Backward Compatibility**
- Existing files without invoice numbers still work
- Old filename format is still supported
- No breaking changes to existing workflows

### ✅ **Edge Case Handling**
- Special characters in invoice numbers are cleaned
- Multiple consecutive underscores are normalized
- Version conflicts are automatically resolved

## Usage Examples

### Processing Files:
```bash
# Process 5 files - all will be preserved with unique names
python3 app/app_ext_textract_llm.py --input-folder data/input --max-files 5

# Results:
# - invoice_12345_extracted.json
# - invoice_67890_extracted.json  
# - bill_ABC123_extracted.json
# - statement_INV001_extracted.json
# - receipt_extracted.json (no invoice number)
```

### Running Evaluation:
```bash
# Evaluation automatically handles new filename format
python3 app/evaluation_extraction.py --use-csv --csv-file data/true_data.csv

# Or use the function directly:
from app.evaluation_extraction import run_csv_evaluation
results = run_csv_evaluation("data/true_data.csv")
```

## Testing

Run the test script to verify functionality:
```bash
python3 test_filename_extraction.py
```

**Test Results**: ✅ Core functionality working correctly
- All basic filename extraction tests pass
- Smart generation prevents overwrites
- Evaluation script correctly maps files

## Technical Details

### Special Character Handling:
- `/` → `_` (forward slash to underscore)
- `-` → `_` (dash to underscore) 
- Multiple `_` → single `_` (normalize)
- Leading/trailing `_` removed

### Invoice Number Detection:
- Must contain at least one digit
- Can be alphanumeric with underscores/dashes
- Must be longer than 1 character
- Cannot be all letters (likely part of original name)

### File Conflict Resolution:
1. Try original filename with invoice number
2. If exists, add version number (`_v1`, `_v2`, etc.)
3. Continue until unique filename found

## Migration Notes

### For Existing Files:
- Old format files (`*_extracted.json`) still work
- New format files (`*_invoice_extracted.json`) are preferred
- Mixed formats are handled automatically

### For Evaluation:
- CSV evaluation automatically detects filename format
- JSON evaluation works with both old and new formats
- No manual intervention required

## Next Steps

1. **Test with your specific files** to verify the solution works
2. **Run evaluation** to confirm correct file matching
3. **Monitor output folder** to see all files are preserved
4. **Adjust max_files** parameter as needed for your processing volume

The solution is now ready for production use and should completely eliminate the file overwrite issue while maintaining full compatibility with your existing evaluation workflow.
