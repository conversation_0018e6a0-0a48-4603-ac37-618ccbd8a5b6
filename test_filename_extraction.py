#!/usr/bin/env python3
"""
Test script for the new filename extraction functionality.
This script tests the smart filename generation and extraction logic.
"""

import sys
import os
import re

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from evaluation_extraction import extract_original_filename_from_extracted

def test_filename_extraction():
    """Test the filename extraction function with various scenarios."""
    
    test_cases = [
        # (input_filename, expected_output, description)
        ("invoice_extracted.json", "invoice", "Simple case"),
        ("invoice_12345_extracted.json", "invoice", "With invoice number"),
        ("invoice_ABC123_extracted.json", "invoice", "With alphanumeric invoice number"),
        ("invoice_12345_extracted_v2.json", "invoice", "With invoice number and version"),
        ("complex_invoice_name_67890_extracted.json", "complex_invoice_name", "Complex name with invoice number"),
        ("file_with_underscores_INV001_extracted.json", "file_with_underscores", "Multiple underscores"),
        ("simple_extracted.json", "simple", "No invoice number"),
        ("test_file_extracted_v3.json", "test_file", "Version without invoice number"),
        ("vendor_invoice_ABC_DEF_123_extracted.json", "vendor_invoice_ABC_DEF", "Multiple parts with invoice number"),
    ]
    
    print("🧪 Testing Filename Extraction Logic")
    print("=" * 60)
    
    all_passed = True
    
    for input_filename, expected_output, description in test_cases:
        result = extract_original_filename_from_extracted(input_filename)
        passed = result == expected_output
        all_passed = all_passed and passed
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {description}")
        print(f"    Input:    {input_filename}")
        print(f"    Expected: {expected_output}")
        print(f"    Got:      {result}")
        print()
    
    print("=" * 60)
    if all_passed:
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed!")
    
    return all_passed

def test_filename_generation_scenarios():
    """Test scenarios for filename generation."""
    
    print("\n📝 Testing Filename Generation Scenarios")
    print("=" * 60)
    
    # Simulate the filename generation logic from the extraction script
    def generate_output_filename(original_name, invoice_number):
        """Simulate the filename generation logic."""
        name_without_ext = os.path.splitext(original_name)[0]
        
        if invoice_number:
            # Clean invoice number for filename (remove special characters)
            clean_invoice_number = re.sub(r'[^\w\-_]', '_', str(invoice_number))
            output_filename = f"{name_without_ext}_{clean_invoice_number}_extracted.json"
        else:
            # Fallback to original filename if no invoice number
            output_filename = f"{name_without_ext}_extracted.json"
        
        return output_filename
    
    test_scenarios = [
        # (original_filename, invoice_number, description)
        ("invoice.pdf", "12345", "Simple PDF with invoice number"),
        ("invoice.pdf", "ABC-123", "PDF with special characters in invoice number"),
        ("invoice.pdf", "", "PDF without invoice number"),
        ("complex_invoice_name.pdf", "INV/001", "Complex name with special chars in invoice"),
        ("vendor_bill.pdf", "2023-001", "Vendor bill with date-like invoice number"),
    ]
    
    for original_filename, invoice_number, description in test_scenarios:
        generated_filename = generate_output_filename(original_filename, invoice_number)
        extracted_original = extract_original_filename_from_extracted(generated_filename)
        
        # The extracted original should match the original name without extension
        expected_original = os.path.splitext(original_filename)[0]
        passed = extracted_original == expected_original
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {description}")
        print(f"    Original:   {original_filename}")
        print(f"    Invoice #:  {invoice_number}")
        print(f"    Generated:  {generated_filename}")
        print(f"    Extracted:  {extracted_original}")
        print(f"    Expected:   {expected_original}")
        print()

def test_edge_cases():
    """Test edge cases and potential issues."""
    
    print("\n🔍 Testing Edge Cases")
    print("=" * 60)
    
    edge_cases = [
        ("file_123_extracted.json", "file", "Number that might be confused with invoice"),
        ("invoice_v1_extracted.json", "invoice", "Version-like suffix in original name"),
        ("test_ABC_DEF_extracted.json", "test_ABC", "Multiple uppercase parts"),
        ("file_with_123_in_name_456_extracted.json", "file_with_123_in_name", "Numbers in original name"),
    ]
    
    for input_filename, expected_output, description in edge_cases:
        result = extract_original_filename_from_extracted(input_filename)
        passed = result == expected_output
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {description}")
        print(f"    Input:    {input_filename}")
        print(f"    Expected: {expected_output}")
        print(f"    Got:      {result}")
        print()

def main():
    """Run all tests."""
    print("🚀 Testing Smart Filename Handling")
    print("=" * 80)
    
    # Test 1: Basic filename extraction
    test1_passed = test_filename_extraction()
    
    # Test 2: Filename generation scenarios
    test_filename_generation_scenarios()
    
    # Test 3: Edge cases
    test_edge_cases()
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 TEST SUMMARY:")
    print(f"   Filename extraction: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    
    if test1_passed:
        print("\n🎉 Core functionality is working correctly!")
        print("\n📌 Key Benefits:")
        print("   • Files with same name but different invoice numbers won't overwrite")
        print("   • Evaluation script can correctly match extracted files to CSV data")
        print("   • Automatic versioning prevents any overwrites")
        print("   • Smart extraction preserves original filename for comparison")
    else:
        print("\n⚠️  Some tests failed. Please review the implementation.")
    
    return test1_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
