# VS Code Terminal Evaluation Guide

This guide shows how to run evaluations directly in VS Code terminal without using command line arguments.

## Quick Start

### 1. CSV Evaluation (Recommended)

Open VS Code terminal and run:

```python
from app.evaluation_extraction import run_csv_evaluation

# Basic usage with default settings
result = run_csv_evaluation("docs/logistically_attachment_data_with_invoice_info.csv")
```

### 2. JSON Evaluation (Original mode)

```python
from app.evaluation_extraction import run_json_evaluation

# Basic usage with default settings
result = run_json_evaluation()
```

## Function Parameters

### `run_csv_evaluation(csv_file_path, extracted_dir, output_file)`

**Parameters:**
- `csv_file_path` (required): Path to CSV file with true data
- `extracted_dir` (optional): Directory with extracted JSON files
  - Default: `"data/output_data/extraction"`
- `output_file` (optional): Output Excel file path
  - Default: Auto-generated with timestamp

**Example:**
```python
result = run_csv_evaluation(
    csv_file_path="docs/logistically_attachment_data_with_invoice_info.csv",
    extracted_dir="data/output_data/extraction",
    output_file="data/evaluation/my_custom_report.xlsx"
)
```

### `run_json_evaluation(extracted_dir, true_dir, output_file)`

**Parameters:**
- `extracted_dir` (optional): Directory with extracted JSON files
  - Default: `"data/output_data/extraction"`
- `true_dir` (optional): Directory with true data JSON files
  - Default: `"data/true_data/extraction"`
- `output_file` (optional): Output Excel file path
  - Default: Auto-generated with timestamp

**Example:**
```python
result = run_json_evaluation(
    extracted_dir="data/output_data/extraction",
    true_dir="data/true_data/extraction",
    output_file="data/evaluation/json_comparison.xlsx"
)
```

## Return Value

Both functions return a dictionary with:

```python
{
    'output_file': 'path/to/generated/report.xlsx',
    'summary': {
        'file_name': {
            'total_fields': 11,
            'correct': 6,
            'null_count': 0,
            'comparable': 11,
            'accuracy': 54.5
        },
        # ... more files
    },
    'total_files': 13
}
```

## Usage Examples

### Example 1: Quick CSV Evaluation
```python
from app.evaluation_extraction import run_csv_evaluation

result = run_csv_evaluation("docs/logistically_attachment_data_with_invoice_info.csv")

if result:
    print(f"✅ Report saved to: {result['output_file']}")
    print(f"📁 Evaluated {result['total_files']} files")
else:
    print("❌ Evaluation failed")
```

### Example 2: Custom Paths
```python
from app.evaluation_extraction import run_csv_evaluation

result = run_csv_evaluation(
    csv_file_path="path/to/your/true_data.csv",
    extracted_dir="path/to/extracted/files",
    output_file="reports/my_evaluation.xlsx"
)
```

### Example 3: Process Results
```python
from app.evaluation_extraction import run_csv_evaluation

result = run_csv_evaluation("docs/logistically_attachment_data_with_invoice_info.csv")

if result:
    print("📈 Detailed Results:")
    for file_name, stats in result['summary'].items():
        accuracy = stats['accuracy']
        correct = stats['correct']
        total = stats['comparable']
        null_count = stats['null_count']
        
        print(f"  {file_name}:")
        print(f"    Accuracy: {accuracy:.1f}% ({correct}/{total})")
        if null_count > 0:
            print(f"    NULL fields: {null_count} (no CSV match)")
```

## Features

### ✅ **CSV Evaluation Features:**
- Compares extracted JSON files against CSV true data
- Matches by invoice number only (strict matching)
- NULL handling for missing invoice numbers (light blue in Excel)
- Auto-generated timestamped output files
- Comprehensive statistics

### ✅ **JSON Evaluation Features:**
- Compares extracted JSON files against true data JSON files
- Field mapping for invoice data
- Confidence score analysis
- Color-coded Excel reports

### ✅ **Output Features:**
- Excel reports with color coding:
  - 🟢 Green: Correct matches (TRUE)
  - 🔴 Red: Incorrect matches (FALSE)
  - 🔵 Light Blue: No CSV match (NULL)
- Statistics summary section
- Auto-adjusted column widths

## Tips for VS Code Usage

1. **Interactive Development**: You can run these functions step by step in VS Code terminal
2. **Custom Analysis**: Access the returned data for custom analysis
3. **Debugging**: Set breakpoints and debug the evaluation process
4. **Batch Processing**: Run multiple evaluations with different parameters

## Troubleshooting

### Common Issues:

1. **File Not Found**: Ensure CSV file path is correct
2. **No Extracted Files**: Check that extracted_dir contains JSON files
3. **Permission Error**: Ensure write permissions for output directory

### Debug Mode:
```python
import logging
logging.basicConfig(level=logging.DEBUG)

result = run_csv_evaluation("docs/logistically_attachment_data_with_invoice_info.csv")
```
