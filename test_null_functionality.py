#!/usr/bin/env python3
"""
Test script to demonstrate the NULL functionality in CSV evaluation mode.
This script creates test files with non-existent invoice numbers to show
how the evaluation handles cases where no CSV match is found.
"""

import json
import os
import subprocess
import sys
from pathlib import Path

def create_test_files():
    """Create test files with non-existent invoice numbers."""
    test_files = [
        {
            "filename": "NO_MATCH_1_extracted.json",
            "data": {
                "vendor_name": "TEST VENDOR 1",
                "invoice_number": "NONEXISTENT001",
                "invoice_date": "2025-08-21",
                "invoice_amount": 100.00,
                "remit_to": {
                    "remit_to_name": "TEST REMIT 1",
                    "remit_to_address1": "123 Test St",
                    "remit_to_address_2": None,
                    "remit_to_city": "Test City",
                    "remit_to_state_province": "TS",
                    "remit_to_postal_code": "12345",
                    "remit_to_country": "USA"
                }
            }
        },
        {
            "filename": "NO_MATCH_2_extracted.json",
            "data": {
                "vendor_name": "TEST VENDOR 2",
                "invoice_number": "NONEXISTENT002",
                "invoice_date": "2025-08-21",
                "invoice_amount": 200.00,
                "remit_to": {
                    "remit_to_name": "TEST REMIT 2",
                    "remit_to_address1": "456 Test Ave",
                    "remit_to_address_2": "Unit B",
                    "remit_to_city": "Test Town",
                    "remit_to_state_province": "TT",
                    "remit_to_postal_code": "67890",
                    "remit_to_country": "USA"
                }
            }
        }
    ]
    
    test_dir = Path("data/output_data/extraction")
    created_files = []
    
    for test_file in test_files:
        file_path = test_dir / test_file["filename"]
        with open(file_path, 'w') as f:
            json.dump(test_file["data"], f, indent=2)
        created_files.append(str(file_path))
        print(f"✅ Created test file: {file_path}")
    
    return created_files

def run_evaluation_with_null_test():
    """Run evaluation to demonstrate NULL functionality."""
    print("\n" + "="*60)
    print("Testing NULL Functionality in CSV Evaluation")
    print("="*60)
    
    cmd = [
        sys.executable,
        "app/evaluation_extraction.py",
        "--use-csv",
        "--csv-file", "docs/logistically_attachment_data_with_invoice_info.csv",
        "--extracted-dir", "data/output_data/extraction",
        "--output-file", "data/evaluation/null_functionality_test.xlsx"
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        print(f"Return code: {result.returncode}")
        
        if result.returncode == 0:
            print("\n✅ NULL functionality test completed successfully!")
            print("📊 Report saved to: data/evaluation/null_functionality_test.xlsx")
            print("\n📋 Key observations:")
            print("   - Files with non-existent invoice numbers show 'NULL' values")
            print("   - NULL entries are colored light blue in the Excel report")
            print("   - Accuracy is calculated only on comparable (non-NULL) fields")
            print("   - Summary shows separate counts for correct, wrong, and NULL fields")
        else:
            print("\n❌ NULL functionality test failed!")
            
    except Exception as e:
        print(f"Error running evaluation: {e}")

def cleanup_test_files(test_files):
    """Remove test files after testing."""
    print("\n" + "="*60)
    print("Cleaning up test files...")
    print("="*60)
    
    for file_path in test_files:
        try:
            os.remove(file_path)
            print(f"🗑️  Removed: {file_path}")
        except Exception as e:
            print(f"❌ Error removing {file_path}: {e}")

def main():
    """Main test function."""
    print("🧪 Testing NULL Functionality in Enhanced Evaluation Script")
    print("This script demonstrates how the evaluation handles files with no CSV matches.\n")
    
    # Create test files with non-existent invoice numbers
    print("📝 Creating test files with non-existent invoice numbers...")
    test_files = create_test_files()
    
    try:
        # Run evaluation
        run_evaluation_with_null_test()
        
        print("\n🎯 Expected Results:")
        print("   - NO_MATCH_1 and NO_MATCH_2 files should show all NULL values")
        print("   - These rows should be colored light blue in the Excel report")
        print("   - Summary should show '0/0 correct (0.0%) | 11 NULL (no CSV match)'")
        print("   - Other files should show normal TRUE/FALSE comparisons")
        
    finally:
        # Clean up test files
        cleanup_test_files(test_files)
    
    print("\n🎉 NULL functionality testing completed!")
    print("\n💡 To manually verify:")
    print("   1. Open data/evaluation/null_functionality_test.xlsx")
    print("   2. Look for NO_MATCH_1 and NO_MATCH_2 entries")
    print("   3. Verify they show 'NULL' values with light blue background")
    print("   4. Check the statistics section for NULL counts")

if __name__ == "__main__":
    main()
