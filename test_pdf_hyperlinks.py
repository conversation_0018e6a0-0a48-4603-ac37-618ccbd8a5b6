#!/usr/bin/env python3
"""
Test script to verify PDF hyperlink functionality in evaluation Excel files.
This script tests the tracking CSV creation and Excel hyperlink generation.
"""

import sys
import os
import csv
import json
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def test_tracking_csv_creation():
    """Test that the tracking CSV is created and contains expected data."""
    
    print("🧪 Testing Tracking CSV Creation")
    print("=" * 50)
    
    tracking_csv_path = "data/output_data/extraction/file_tracking.csv"
    
    if not os.path.exists(tracking_csv_path):
        print(f"❌ Tracking CSV not found at: {tracking_csv_path}")
        print("   Run the extraction script first to generate tracking data")
        return False
    
    try:
        with open(tracking_csv_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            rows = list(reader)
            
        print(f"✅ Found tracking CSV with {len(rows)} entries")
        
        if len(rows) > 0:
            print("\n📋 Sample tracking data:")
            sample_row = rows[0]
            for key, value in sample_row.items():
                print(f"   {key}: {value}")
            
            # Check required columns
            required_columns = ['timestamp', 'original_pdf_path', 'moved_pdf_path', 
                              'extracted_json_path', 'extracted_json_filename', 
                              'invoice_number', 'vendor_name', 'processing_status']
            
            missing_columns = [col for col in required_columns if col not in sample_row.keys()]
            if missing_columns:
                print(f"❌ Missing required columns: {missing_columns}")
                return False
            else:
                print("✅ All required columns present")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading tracking CSV: {e}")
        return False

def test_tracking_csv_loading():
    """Test that the evaluation script can load tracking data correctly."""
    
    print("\n🔍 Testing Tracking CSV Loading")
    print("=" * 50)
    
    try:
        from evaluation_extraction import load_tracking_csv
        
        tracking_data = load_tracking_csv()
        
        if not tracking_data:
            print("❌ No tracking data loaded")
            return False
        
        print(f"✅ Loaded tracking data for {len(tracking_data)} files")
        
        # Show sample data
        if tracking_data:
            sample_key = list(tracking_data.keys())[0]
            sample_data = tracking_data[sample_key]
            print(f"\n📋 Sample tracking entry for '{sample_key}':")
            for key, value in sample_data.items():
                print(f"   {key}: {value}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error loading tracking data: {e}")
        return False

def test_filename_matching():
    """Test that extracted JSON filenames can be matched to original filenames."""
    
    print("\n🎯 Testing Filename Matching")
    print("=" * 50)
    
    try:
        from evaluation_extraction import load_tracking_csv, extract_original_filename_from_extracted
        
        tracking_data = load_tracking_csv()
        
        if not tracking_data:
            print("❌ No tracking data available for testing")
            return False
        
        successful_matches = 0
        total_files = len(tracking_data)
        
        for json_filename, info in tracking_data.items():
            # Extract original filename from JSON filename
            original_from_json = extract_original_filename_from_extracted(json_filename)
            
            # Get the actual original filename from tracking data
            original_pdf_path = info.get('original_pdf_path', '')
            actual_original = os.path.splitext(os.path.basename(original_pdf_path))[0] if original_pdf_path else ''
            
            if original_from_json == actual_original:
                successful_matches += 1
                print(f"✅ {json_filename} → {original_from_json}")
            else:
                print(f"❌ {json_filename} → {original_from_json} (expected: {actual_original})")
        
        success_rate = (successful_matches / total_files) * 100 if total_files > 0 else 0
        print(f"\n📊 Matching Results:")
        print(f"   Successful matches: {successful_matches}/{total_files}")
        print(f"   Success rate: {success_rate:.1f}%")
        
        return success_rate > 80  # Consider 80%+ success rate as passing
        
    except Exception as e:
        print(f"❌ Error testing filename matching: {e}")
        return False

def test_pdf_file_existence():
    """Test that PDF files referenced in tracking data actually exist."""
    
    print("\n📁 Testing PDF File Existence")
    print("=" * 50)
    
    try:
        from evaluation_extraction import load_tracking_csv
        
        tracking_data = load_tracking_csv()
        
        if not tracking_data:
            print("❌ No tracking data available for testing")
            return False
        
        existing_files = 0
        total_files = len(tracking_data)
        
        for json_filename, info in tracking_data.items():
            moved_pdf_path = info.get('moved_pdf_path', '')
            original_pdf_path = info.get('original_pdf_path', '')
            
            # Check moved path first, then original path
            pdf_path = moved_pdf_path if moved_pdf_path else original_pdf_path
            
            if pdf_path and os.path.exists(pdf_path):
                existing_files += 1
                print(f"✅ {os.path.basename(pdf_path)} exists at {pdf_path}")
            else:
                print(f"❌ {json_filename} → PDF not found at {pdf_path}")
        
        existence_rate = (existing_files / total_files) * 100 if total_files > 0 else 0
        print(f"\n📊 PDF Existence Results:")
        print(f"   Existing PDFs: {existing_files}/{total_files}")
        print(f"   Existence rate: {existence_rate:.1f}%")
        
        return existence_rate > 70  # Consider 70%+ existence rate as passing
        
    except Exception as e:
        print(f"❌ Error testing PDF file existence: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing PDF Hyperlink Functionality")
    print("=" * 80)
    
    # Test 1: Tracking CSV creation
    test1_passed = test_tracking_csv_creation()
    
    # Test 2: Tracking CSV loading
    test2_passed = test_tracking_csv_loading()
    
    # Test 3: Filename matching
    test3_passed = test_filename_matching()
    
    # Test 4: PDF file existence
    test4_passed = test_pdf_file_existence()
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 TEST SUMMARY:")
    print(f"   Tracking CSV creation: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"   Tracking CSV loading: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    print(f"   Filename matching: {'✅ PASS' if test3_passed else '❌ FAIL'}")
    print(f"   PDF file existence: {'✅ PASS' if test4_passed else '❌ FAIL'}")
    
    all_passed = test1_passed and test2_passed and test3_passed and test4_passed
    
    if all_passed:
        print("\n🎉 All tests passed! PDF hyperlink functionality is ready.")
        print("\n📌 Next Steps:")
        print("   1. Run extraction on some files to populate tracking data")
        print("   2. Run evaluation to generate Excel with PDF hyperlinks")
        print("   3. Open Excel file and test the PDF hyperlinks")
    else:
        print("\n⚠️  Some tests failed. Check the implementation.")
        if not test1_passed:
            print("   • Run extraction script first to generate tracking data")
        if not test4_passed:
            print("   • Check that PDF files haven't been moved or deleted")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
